#!/usr/bin/env python3
"""
调试按钮问题
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from logic_operations import PythonCodeEditorAdvanced

def debug_node_creation():
    """调试节点创建"""
    print("🔍 调试节点创建")
    print("=" * 50)
    
    editor = PythonCodeEditorAdvanced()
    
    # 检查INPUT_TYPES
    input_types = editor.INPUT_TYPES()
    
    print("📋 当前节点配置:")
    print(f"  节点类名: {editor.__class__.__name__}")
    print(f"  函数名: {editor.FUNCTION}")
    print(f"  分类: {editor.CATEGORY}")
    print(f"  返回类型: {editor.RETURN_TYPES}")
    print(f"  返回名称: {editor.RETURN_NAMES}")
    
    print("\n📋 输入参数:")
    for section, params in input_types.items():
        print(f"  {section}:")
        for param_name, param_config in params.items():
            print(f"    {param_name}: {param_config}")
    
    print()

def check_javascript_file():
    """检查JavaScript文件内容"""
    print("📁 检查JavaScript文件内容")
    print("=" * 50)
    
    js_file = "web/extensions/python_code_editor.js"
    
    if os.path.exists(js_file):
        print(f"✅ JavaScript文件存在: {js_file}")
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键部分
        key_checks = [
            ("PythonCodeEditorAdvanced", "节点名称检测"),
            ("addWidget", "addWidget方法"),
            ('"button"', "button类型"),
            ("🚀 Open External Editor", "按钮文本"),
            ("openExternalEditor", "外部编辑器函数"),
            ("serialize = false", "序列化禁用"),
            ("splice", "widget移除逻辑")
        ]
        
        print("\n🔍 关键代码检查:")
        for check_text, description in key_checks:
            if check_text in content:
                print(f"  ✅ {description}: 存在")
            else:
                print(f"  ❌ {description}: 不存在")
        
        # 查找addWidget调用的具体位置
        lines = content.split('\n')
        print(f"\n📍 addWidget调用位置:")
        for i, line in enumerate(lines, 1):
            if 'addWidget' in line:
                print(f"  第{i}行: {line.strip()}")
        
        # 查找节点名称匹配的位置
        print(f"\n📍 节点名称匹配位置:")
        for i, line in enumerate(lines, 1):
            if 'PythonCodeEditorAdvanced' in line:
                print(f"  第{i}行: {line.strip()}")
                
    else:
        print(f"❌ JavaScript文件不存在: {js_file}")
    
    print()

def create_simple_test_js():
    """创建一个简单的测试JavaScript文件"""
    print("📝 创建简单测试JavaScript")
    print("=" * 50)
    
    simple_js = """
// 简单的按钮测试
import { app } from "../../scripts/app.js";

app.registerExtension({
    name: "ComfyUI.SimpleButtonTest",
    
    async beforeRegisterNodeDef(nodeType, nodeData, app) {
        if (nodeData.name === "PythonCodeEditorAdvanced") {
            console.log("🔍 检测到PythonCodeEditorAdvanced节点");
            
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function() {
                const result = onNodeCreated?.apply(this, arguments);
                
                console.log("🔧 节点创建中...");
                console.log("📋 当前widgets:", this.widgets?.map(w => w.name));
                
                // 尝试添加按钮
                try {
                    const buttonWidget = this.addWidget("button", "🚀 Test Button", null, () => {
                        console.log("🔘 按钮被点击了!");
                        alert("按钮工作正常!");
                    });
                    
                    if (buttonWidget) {
                        buttonWidget.serialize = false;
                        console.log("✅ 按钮添加成功");
                    } else {
                        console.log("❌ 按钮添加失败");
                    }
                } catch (error) {
                    console.error("❌ 添加按钮时出错:", error);
                }
                
                return result;
            };
        }
    }
});

console.log("🧪 简单按钮测试扩展已加载");
"""
    
    test_js_file = "web/extensions/simple_button_test.js"
    with open(test_js_file, 'w', encoding='utf-8') as f:
        f.write(simple_js)
    
    print(f"✅ 创建了测试文件: {test_js_file}")
    print("📋 测试文件功能:")
    print("  - 检测PythonCodeEditorAdvanced节点")
    print("  - 添加简单的测试按钮")
    print("  - 输出调试信息到控制台")
    print("  - 点击按钮时显示alert")
    
    print("\n🔧 使用方法:")
    print("  1. 重启ComfyUI")
    print("  2. 打开浏览器开发者工具(F12)")
    print("  3. 查看控制台输出")
    print("  4. 添加Python Code Editor (Advanced)节点")
    print("  5. 查看是否有测试按钮出现")
    
    print()

def show_debugging_steps():
    """显示调试步骤"""
    print("🔧 调试步骤")
    print("=" * 50)
    
    print("📋 问题分析:")
    print("  1. open_external_editor参数已成功移除")
    print("  2. JavaScript代码看起来正确")
    print("  3. 可能的问题:")
    print("     - JavaScript文件没有正确加载")
    print("     - addWidget方法调用有问题")
    print("     - 节点名称匹配有问题")
    print("     - 时序问题(节点创建时机)")
    
    print("\n🔍 调试方法:")
    print("  1. 检查浏览器控制台是否有错误")
    print("  2. 确认JavaScript文件是否加载")
    print("  3. 使用简单测试文件验证")
    print("  4. 检查ComfyUI版本兼容性")
    
    print("\n📝 下一步:")
    print("  1. 重启ComfyUI")
    print("  2. 打开浏览器开发者工具")
    print("  3. 添加节点并查看控制台输出")
    print("  4. 如果简单测试按钮出现，说明方法正确")
    print("  5. 如果没有出现，检查JavaScript加载问题")
    
    print()

if __name__ == "__main__":
    print("🔍 按钮调试工具")
    print("=" * 60)
    print()
    
    debug_node_creation()
    check_javascript_file()
    create_simple_test_js()
    show_debugging_steps()
    
    print("🎊 调试准备完成!")
    print("\n📋 总结:")
    print("✅ 节点配置正确")
    print("✅ JavaScript代码存在")
    print("✅ 创建了简单测试文件")
    print("🔍 需要在ComfyUI中测试")
    print("\n🚀 请重启ComfyUI并查看浏览器控制台输出!")
