// Python Code Editor Extension for ComfyUI
// Provides enhanced code editing experience

import { app } from "../../scripts/app.js";
import { ComfyWidgets } from "../../scripts/widgets.js";

// Load CSS styles
function loadCSS() {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = '/extensions/ComfyUI-Common-Utility/python_code_editor.css';
    document.head.appendChild(link);
}

// Add button to node using a more reliable method
function addExternalEditorButton(node, codeWidget) {
    // Store the button creation function on the node for later use
    node.addExternalEditorButton = () => {
        // Check if button already exists
        if (node.externalEditorButton) return;

        // Create button container
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'external-editor-button-container';
        buttonContainer.style.cssText = `
            margin: 5px;
            text-align: center;
            padding: 5px;
        `;

        // Create the button
        const button = document.createElement('button');
        button.className = 'external-editor-button';
        button.textContent = '🚀 Open External Editor';
        button.style.cssText = `
            background-color: #0e639c;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 12px;
            font-family: Arial, sans-serif;
            transition: background-color 0.2s ease;
            width: 100%;
            box-sizing: border-box;
        `;

        // Add hover effects
        button.addEventListener('mouseover', () => {
            button.style.backgroundColor = '#1177bb';
        });

        button.addEventListener('mouseout', () => {
            button.style.backgroundColor = '#0e639c';
        });

        // Add click handler
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const currentCode = codeWidget ? codeWidget.value : "";
            openExternalEditor(currentCode, (newCode) => {
                if (codeWidget) {
                    codeWidget.value = newCode;
                    // Trigger the callback to update the node
                    if (codeWidget.callback) {
                        codeWidget.callback(newCode);
                    }
                }
            });
        });

        buttonContainer.appendChild(button);

        // Store reference to prevent duplicate creation
        node.externalEditorButton = buttonContainer;

        return buttonContainer;
    };

    // Try to add the button using multiple methods

    // Method 1: Try to add to existing DOM element
    setTimeout(() => {
        if (node.externalEditorButton) return; // Already added

        const nodeElements = document.querySelectorAll('[data-title*="Python Code Editor (Advanced)"]');
        for (const element of nodeElements) {
            if (!element.querySelector('.external-editor-button')) {
                const buttonContainer = node.addExternalEditorButton();
                if (buttonContainer) {
                    element.appendChild(buttonContainer);
                    console.log("External editor button added via method 1");
                    return;
                }
            }
        }

        // Method 2: Try alternative selector
        const allNodes = document.querySelectorAll('.comfy-node');
        for (const element of allNodes) {
            const titleElement = element.querySelector('.comfy-title');
            if (titleElement && titleElement.textContent.includes('Python Code Editor (Advanced)')) {
                if (!element.querySelector('.external-editor-button')) {
                    const buttonContainer = node.addExternalEditorButton();
                    if (buttonContainer) {
                        element.appendChild(buttonContainer);
                        console.log("External editor button added via method 2");
                        return;
                    }
                }
            }
        }

        console.log("Could not find target node element for external editor button");
    }, 1000);

    // Method 3: Add button when node is drawn (fallback)
    const originalOnDrawBackground = node.onDrawBackground;
    node.onDrawBackground = function (ctx) {
        if (originalOnDrawBackground) {
            originalOnDrawBackground.call(this, ctx);
        }

        // Try to add button if not already added
        if (!this.externalEditorButton) {
            setTimeout(() => {
                const nodeElement = document.querySelector(`[data-id="${this.id}"]`);
                if (nodeElement && !nodeElement.querySelector('.external-editor-button')) {
                    const buttonContainer = this.addExternalEditorButton();
                    if (buttonContainer) {
                        nodeElement.appendChild(buttonContainer);
                        console.log("External editor button added via method 3");
                    }
                }
            }, 100);
        }
    };
}



// Enhanced code editor widget
function createCodeEditorWidget(node, inputName, inputData, app) {
    const widget = ComfyWidgets.STRING(node, inputName, inputData, app);

    // Store original widget
    const originalWidget = { ...widget };

    // Add enhanced functionality
    widget.type = "python_code_editor";

    // Create enhanced text area
    const createEnhancedTextArea = () => {
        const textarea = document.createElement("textarea");
        textarea.className = "comfy-multiline-input python-code-editor";
        textarea.style.cssText = `
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            background-color: #1e1e1e;
            color: #d4d4d4;
            border: 1px solid #3c3c3c;
            border-radius: 4px;
            padding: 8px;
            tab-size: 4;
            white-space: pre;
            overflow-wrap: normal;
            overflow-x: auto;
            resize: both;
            min-height: 200px;
            width: 100%;
        `;

        // Add syntax highlighting classes
        textarea.addEventListener('input', function () {
            // Basic syntax highlighting could be added here
            // For now, we'll just ensure proper formatting
            this.style.height = 'auto';
            this.style.height = Math.max(200, this.scrollHeight) + 'px';
        });

        // Add keyboard shortcuts
        textarea.addEventListener('keydown', function (e) {
            // Tab key handling
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = this.selectionStart;
                const end = this.selectionEnd;

                if (e.shiftKey) {
                    // Shift+Tab: Remove indentation
                    const lines = this.value.split('\n');
                    const startLine = this.value.substring(0, start).split('\n').length - 1;
                    const endLine = this.value.substring(0, end).split('\n').length - 1;

                    for (let i = startLine; i <= endLine; i++) {
                        if (lines[i].startsWith('    ')) {
                            lines[i] = lines[i].substring(4);
                        } else if (lines[i].startsWith('\t')) {
                            lines[i] = lines[i].substring(1);
                        }
                    }

                    this.value = lines.join('\n');
                } else {
                    // Tab: Add indentation
                    this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                    this.selectionStart = this.selectionEnd = start + 4;
                }
            }

            // Ctrl+Enter: Add new line (instead of executing)
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                const start = this.selectionStart;
                this.value = this.value.substring(0, start) + '\n' + this.value.substring(start);
                this.selectionStart = this.selectionEnd = start + 1;
            }

            // Auto-indentation on Enter
            if (e.key === 'Enter' && !e.ctrlKey) {
                e.preventDefault();
                const start = this.selectionStart;
                const lines = this.value.substring(0, start).split('\n');
                const currentLine = lines[lines.length - 1];
                const indent = currentLine.match(/^(\s*)/)[1];

                // Add extra indent for certain keywords
                const extraIndent = /:\s*$/.test(currentLine.trim()) ? '    ' : '';

                this.value = this.value.substring(0, start) + '\n' + indent + extraIndent + this.value.substring(start);
                this.selectionStart = this.selectionEnd = start + 1 + indent.length + extraIndent.length;
            }
        });

        return textarea;
    };

    // Override the widget's element creation
    const originalCallback = widget.callback;
    widget.callback = function (value) {
        if (originalCallback) {
            originalCallback(value);
        }
    };

    return widget;
}

// Create external editor window
function openExternalEditor(currentCode, callback) {
    const editorWindow = window.open('', 'PythonCodeEditor', 'width=800,height=600,scrollbars=yes,resizable=yes');

    editorWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Python Code Editor</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    background-color: #1e1e1e;
                    color: #d4d4d4;
                }
                .editor-container {
                    display: flex;
                    flex-direction: column;
                    height: 100vh;
                }
                .toolbar {
                    background-color: #2d2d30;
                    padding: 8px;
                    border-bottom: 1px solid #3c3c3c;
                    display: flex;
                    gap: 8px;
                    align-items: center;
                }
                .toolbar button {
                    background-color: #0e639c;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                }
                .toolbar button:hover {
                    background-color: #1177bb;
                }
                .toolbar .info {
                    color: #cccccc;
                    font-size: 12px;
                    margin-left: auto;
                }
                #codeEditor {
                    flex: 1;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    font-size: 14px;
                    line-height: 1.4;
                    background-color: #1e1e1e;
                    color: #d4d4d4;
                    border: none;
                    padding: 16px;
                    tab-size: 4;
                    white-space: pre;
                    overflow-wrap: normal;
                    resize: none;
                    outline: none;
                }
                .status-bar {
                    background-color: #007acc;
                    color: white;
                    padding: 4px 8px;
                    font-size: 12px;
                    display: flex;
                    justify-content: space-between;
                }
            </style>
        </head>
        <body>
            <div class="editor-container">
                <div class="toolbar">
                    <button onclick="saveAndClose()">Save & Close</button>
                    <button onclick="checkSyntax()">Check Syntax</button>
                    <button onclick="insertTemplate()">Insert Template</button>
                    <div class="info">Python Code Editor - Use Ctrl+S to save</div>
                </div>
                <textarea id="codeEditor" placeholder="# Write your Python code here...">${currentCode}</textarea>
                <div class="status-bar">
                    <span id="status">Ready</span>
                    <span id="position">Line 1, Column 1</span>
                </div>
            </div>
            
            <script>
                const editor = document.getElementById('codeEditor');
                const statusElement = document.getElementById('status');
                const positionElement = document.getElementById('position');
                
                // Update cursor position
                editor.addEventListener('selectionchange', updatePosition);
                editor.addEventListener('keyup', updatePosition);
                editor.addEventListener('click', updatePosition);
                
                function updatePosition() {
                    const text = editor.value;
                    const pos = editor.selectionStart;
                    const lines = text.substring(0, pos).split('\\n');
                    const line = lines.length;
                    const column = lines[lines.length - 1].length + 1;
                    positionElement.textContent = 'Line ' + line + ', Column ' + column;
                }
                
                // Keyboard shortcuts
                editor.addEventListener('keydown', function(e) {
                    // Ctrl+S: Save
                    if (e.ctrlKey && e.key === 's') {
                        e.preventDefault();
                        saveAndClose();
                    }
                    
                    // Tab handling
                    if (e.key === 'Tab') {
                        e.preventDefault();
                        const start = this.selectionStart;
                        const end = this.selectionEnd;
                        
                        if (e.shiftKey) {
                            // Shift+Tab: Remove indentation
                            const lines = this.value.split('\\n');
                            const startLine = this.value.substring(0, start).split('\\n').length - 1;
                            const endLine = this.value.substring(0, end).split('\\n').length - 1;
                            
                            for (let i = startLine; i <= endLine; i++) {
                                if (lines[i].startsWith('    ')) {
                                    lines[i] = lines[i].substring(4);
                                } else if (lines[i].startsWith('\\t')) {
                                    lines[i] = lines[i].substring(1);
                                }
                            }
                            
                            this.value = lines.join('\\n');
                        } else {
                            // Tab: Add indentation
                            this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                            this.selectionStart = this.selectionEnd = start + 4;
                        }
                    }
                    
                    // Auto-indentation on Enter
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const start = this.selectionStart;
                        const lines = this.value.substring(0, start).split('\\n');
                        const currentLine = lines[lines.length - 1];
                        const indent = currentLine.match(/^(\\s*)/)[1];
                        
                        // Add extra indent for certain keywords
                        const extraIndent = /:\\s*$/.test(currentLine.trim()) ? '    ' : '';
                        
                        this.value = this.value.substring(0, start) + '\\n' + indent + extraIndent + this.value.substring(start);
                        this.selectionStart = this.selectionEnd = start + 1 + indent.length + extraIndent.length;
                    }
                });
                
                function saveAndClose() {
                    window.opener.postMessage({
                        type: 'pythonCodeEditor',
                        code: editor.value
                    }, '*');
                    window.close();
                }
                
                function checkSyntax() {
                    statusElement.textContent = 'Checking syntax...';
                    // Basic syntax check (simplified)
                    try {
                        // This is a very basic check - in a real implementation,
                        // you'd send this to a Python syntax checker
                        const code = editor.value;
                        if (code.trim() === '') {
                            statusElement.textContent = 'Empty code';
                            return;
                        }
                        
                        // Check for basic syntax issues
                        const lines = code.split('\\n');
                        let indentLevel = 0;
                        let hasError = false;
                        
                        for (let i = 0; i < lines.length; i++) {
                            const line = lines[i];
                            if (line.trim() === '') continue;
                            
                            // Basic indentation check
                            const currentIndent = (line.match(/^\\s*/) || [''])[0].length;
                            
                            // Check for unmatched parentheses, brackets, braces
                            const openCount = (line.match(/[\\(\\[\\{]/g) || []).length;
                            const closeCount = (line.match(/[\\)\\]\\}]/g) || []).length;
                            
                            if (line.trim().endsWith(':')) {
                                indentLevel = currentIndent + 4;
                            }
                        }
                        
                        statusElement.textContent = 'Syntax appears valid';
                    } catch (e) {
                        statusElement.textContent = 'Syntax check failed: ' + e.message;
                    }
                }
                
                function insertTemplate() {
                    const templates = [
                        '# Basic template\\nresult = "Hello World"\\nprint(result)',
                        '# Array processing\\nif input_array:\\n    result = f"Array has {len(input_array)} items"\\nelse:\\n    result = "No array provided"',
                        '# Data processing\\nimport json\\ndata = json.loads(input_data) if input_data else {}\\nresult = f"Processed {len(data)} items"',
                        '# Mathematical operations\\nimport math\\nresult = math.sqrt(input_number) if input_number > 0 else 0'
                    ];
                    
                    const choice = prompt('Choose template:\\n0: Basic\\n1: Array processing\\n2: Data processing\\n3: Math operations');
                    if (choice !== null && choice >= 0 && choice < templates.length) {
                        editor.value = templates[parseInt(choice)];
                        updatePosition();
                    }
                }
                
                // Focus the editor
                editor.focus();
                updatePosition();
            </script>
        </body>
        </html>
    `);

    // Listen for messages from the editor window
    window.addEventListener('message', function (event) {
        if (event.data.type === 'pythonCodeEditor') {
            callback(event.data.code);
        }
    });
}

// Initialize the extension
loadCSS();

// Register the extension
app.registerExtension({
    name: "ComfyUI.PythonCodeEditor",

    async beforeRegisterNodeDef(nodeType, nodeData, app) {
        if (nodeData.name === "PythonCodeEditor" || nodeData.name === "PythonCodeEditorAdvanced") {
            // Override the widget creation for python_code input
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function () {
                const result = onNodeCreated?.apply(this, arguments);

                // Find the python_code widget
                const codeWidget = this.widgets?.find(w => w.name === "python_code");
                if (codeWidget) {
                    // Enhance the widget
                    codeWidget.type = "python_code_editor";

                    // Add external editor button for advanced version
                    if (nodeData.name === "PythonCodeEditorAdvanced") {
                        // Remove the toggle widget if it exists
                        const toggleWidget = this.widgets?.find(w => w.name === "open_external_editor");
                        if (toggleWidget) {
                            const index = this.widgets.indexOf(toggleWidget);
                            this.widgets.splice(index, 1);
                        }

                        // Add a real button widget
                        const buttonWidget = this.addWidget("button", "🚀 Open External Editor", null, () => {
                            const currentCode = codeWidget ? codeWidget.value : "";
                            openExternalEditor(currentCode, (newCode) => {
                                if (codeWidget) {
                                    codeWidget.value = newCode;
                                    // Trigger the callback to update the node
                                    if (codeWidget.callback) {
                                        codeWidget.callback(newCode);
                                    }
                                }
                            });
                        });

                        // Style the button
                        if (buttonWidget) {
                            buttonWidget.serialize = false; // Don't save button state
                        }
                    }
                }

                return result;
            };
        }
    }
});

console.log("Python Code Editor extension loaded");
