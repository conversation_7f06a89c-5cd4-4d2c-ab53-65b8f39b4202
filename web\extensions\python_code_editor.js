// Python Code Editor Extension for ComfyUI
// Provides enhanced code editing experience

import { app } from "../../scripts/app.js";
import { ComfyWidgets } from "../../scripts/widgets.js";

// Load CSS styles
function loadCSS() {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = '/extensions/ComfyUI-Common-Utility/python_code_editor.css';
    document.head.appendChild(link);
}

// Create a custom button widget
function createButtonWidget(node, name, text, callback) {
    const widget = {
        type: "button",
        name: name,
        value: text,
        callback: callback,
        options: {},
        draw: function (ctx, node, widgetWidth, y, widgetHeight) {
            const margin = 10;
            const buttonWidth = widgetWidth - margin * 2;
            const buttonHeight = widgetHeight - 4;

            // Button background
            ctx.fillStyle = "#0e639c";
            ctx.fillRect(margin, y + 2, buttonWidth, buttonHeight);

            // Button border
            ctx.strokeStyle = "#1177bb";
            ctx.lineWidth = 1;
            ctx.strokeRect(margin, y + 2, buttonWidth, buttonHeight);

            // Button text
            ctx.fillStyle = "#ffffff";
            ctx.font = "12px Arial";
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(this.value, margin + buttonWidth / 2, y + buttonHeight / 2 + 2);
        },
        mouse: function (event, pos, node) {
            if (event.type === "pointerdown") {
                if (this.callback) {
                    this.callback();
                }
                return true;
            }
            return false;
        },
        computeSize: function (width) {
            return [width, 30];
        }
    };

    return widget;
}

// Enhanced code editor widget
function createCodeEditorWidget(node, inputName, inputData, app) {
    const widget = ComfyWidgets.STRING(node, inputName, inputData, app);

    // Store original widget
    const originalWidget = { ...widget };

    // Add enhanced functionality
    widget.type = "python_code_editor";

    // Create enhanced text area
    const createEnhancedTextArea = () => {
        const textarea = document.createElement("textarea");
        textarea.className = "comfy-multiline-input python-code-editor";
        textarea.style.cssText = `
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            background-color: #1e1e1e;
            color: #d4d4d4;
            border: 1px solid #3c3c3c;
            border-radius: 4px;
            padding: 8px;
            tab-size: 4;
            white-space: pre;
            overflow-wrap: normal;
            overflow-x: auto;
            resize: both;
            min-height: 200px;
            width: 100%;
        `;

        // Add syntax highlighting classes
        textarea.addEventListener('input', function () {
            // Basic syntax highlighting could be added here
            // For now, we'll just ensure proper formatting
            this.style.height = 'auto';
            this.style.height = Math.max(200, this.scrollHeight) + 'px';
        });

        // Add keyboard shortcuts
        textarea.addEventListener('keydown', function (e) {
            // Tab key handling
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = this.selectionStart;
                const end = this.selectionEnd;

                if (e.shiftKey) {
                    // Shift+Tab: Remove indentation
                    const lines = this.value.split('\n');
                    const startLine = this.value.substring(0, start).split('\n').length - 1;
                    const endLine = this.value.substring(0, end).split('\n').length - 1;

                    for (let i = startLine; i <= endLine; i++) {
                        if (lines[i].startsWith('    ')) {
                            lines[i] = lines[i].substring(4);
                        } else if (lines[i].startsWith('\t')) {
                            lines[i] = lines[i].substring(1);
                        }
                    }

                    this.value = lines.join('\n');
                } else {
                    // Tab: Add indentation
                    this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                    this.selectionStart = this.selectionEnd = start + 4;
                }
            }

            // Ctrl+Enter: Add new line (instead of executing)
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                const start = this.selectionStart;
                this.value = this.value.substring(0, start) + '\n' + this.value.substring(start);
                this.selectionStart = this.selectionEnd = start + 1;
            }

            // Auto-indentation on Enter
            if (e.key === 'Enter' && !e.ctrlKey) {
                e.preventDefault();
                const start = this.selectionStart;
                const lines = this.value.substring(0, start).split('\n');
                const currentLine = lines[lines.length - 1];
                const indent = currentLine.match(/^(\s*)/)[1];

                // Add extra indent for certain keywords
                const extraIndent = /:\s*$/.test(currentLine.trim()) ? '    ' : '';

                this.value = this.value.substring(0, start) + '\n' + indent + extraIndent + this.value.substring(start);
                this.selectionStart = this.selectionEnd = start + 1 + indent.length + extraIndent.length;
            }
        });

        return textarea;
    };

    // Override the widget's element creation
    const originalCallback = widget.callback;
    widget.callback = function (value) {
        if (originalCallback) {
            originalCallback(value);
        }
    };

    return widget;
}

// Create external editor window
function openExternalEditor(currentCode, callback) {
    const editorWindow = window.open('', 'PythonCodeEditor', 'width=800,height=600,scrollbars=yes,resizable=yes');

    editorWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Python Code Editor</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    background-color: #1e1e1e;
                    color: #d4d4d4;
                }
                .editor-container {
                    display: flex;
                    flex-direction: column;
                    height: 100vh;
                }
                .toolbar {
                    background-color: #2d2d30;
                    padding: 8px;
                    border-bottom: 1px solid #3c3c3c;
                    display: flex;
                    gap: 8px;
                    align-items: center;
                }
                .toolbar button {
                    background-color: #0e639c;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                }
                .toolbar button:hover {
                    background-color: #1177bb;
                }
                .toolbar .info {
                    color: #cccccc;
                    font-size: 12px;
                    margin-left: auto;
                }
                #codeEditor {
                    flex: 1;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    font-size: 14px;
                    line-height: 1.4;
                    background-color: #1e1e1e;
                    color: #d4d4d4;
                    border: none;
                    padding: 16px;
                    tab-size: 4;
                    white-space: pre;
                    overflow-wrap: normal;
                    resize: none;
                    outline: none;
                }
                .status-bar {
                    background-color: #007acc;
                    color: white;
                    padding: 4px 8px;
                    font-size: 12px;
                    display: flex;
                    justify-content: space-between;
                }
            </style>
        </head>
        <body>
            <div class="editor-container">
                <div class="toolbar">
                    <button onclick="saveAndClose()">Save & Close</button>
                    <button onclick="checkSyntax()">Check Syntax</button>
                    <button onclick="insertTemplate()">Insert Template</button>
                    <div class="info">Python Code Editor - Use Ctrl+S to save</div>
                </div>
                <textarea id="codeEditor" placeholder="# Write your Python code here...">${currentCode}</textarea>
                <div class="status-bar">
                    <span id="status">Ready</span>
                    <span id="position">Line 1, Column 1</span>
                </div>
            </div>
            
            <script>
                const editor = document.getElementById('codeEditor');
                const statusElement = document.getElementById('status');
                const positionElement = document.getElementById('position');
                
                // Update cursor position
                editor.addEventListener('selectionchange', updatePosition);
                editor.addEventListener('keyup', updatePosition);
                editor.addEventListener('click', updatePosition);
                
                function updatePosition() {
                    const text = editor.value;
                    const pos = editor.selectionStart;
                    const lines = text.substring(0, pos).split('\\n');
                    const line = lines.length;
                    const column = lines[lines.length - 1].length + 1;
                    positionElement.textContent = 'Line ' + line + ', Column ' + column;
                }
                
                // Keyboard shortcuts
                editor.addEventListener('keydown', function(e) {
                    // Ctrl+S: Save
                    if (e.ctrlKey && e.key === 's') {
                        e.preventDefault();
                        saveAndClose();
                    }
                    
                    // Tab handling
                    if (e.key === 'Tab') {
                        e.preventDefault();
                        const start = this.selectionStart;
                        const end = this.selectionEnd;
                        
                        if (e.shiftKey) {
                            // Shift+Tab: Remove indentation
                            const lines = this.value.split('\\n');
                            const startLine = this.value.substring(0, start).split('\\n').length - 1;
                            const endLine = this.value.substring(0, end).split('\\n').length - 1;
                            
                            for (let i = startLine; i <= endLine; i++) {
                                if (lines[i].startsWith('    ')) {
                                    lines[i] = lines[i].substring(4);
                                } else if (lines[i].startsWith('\\t')) {
                                    lines[i] = lines[i].substring(1);
                                }
                            }
                            
                            this.value = lines.join('\\n');
                        } else {
                            // Tab: Add indentation
                            this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                            this.selectionStart = this.selectionEnd = start + 4;
                        }
                    }
                    
                    // Auto-indentation on Enter
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const start = this.selectionStart;
                        const lines = this.value.substring(0, start).split('\\n');
                        const currentLine = lines[lines.length - 1];
                        const indent = currentLine.match(/^(\\s*)/)[1];
                        
                        // Add extra indent for certain keywords
                        const extraIndent = /:\\s*$/.test(currentLine.trim()) ? '    ' : '';
                        
                        this.value = this.value.substring(0, start) + '\\n' + indent + extraIndent + this.value.substring(start);
                        this.selectionStart = this.selectionEnd = start + 1 + indent.length + extraIndent.length;
                    }
                });
                
                function saveAndClose() {
                    window.opener.postMessage({
                        type: 'pythonCodeEditor',
                        code: editor.value
                    }, '*');
                    window.close();
                }
                
                function checkSyntax() {
                    statusElement.textContent = 'Checking syntax...';
                    // Basic syntax check (simplified)
                    try {
                        // This is a very basic check - in a real implementation,
                        // you'd send this to a Python syntax checker
                        const code = editor.value;
                        if (code.trim() === '') {
                            statusElement.textContent = 'Empty code';
                            return;
                        }
                        
                        // Check for basic syntax issues
                        const lines = code.split('\\n');
                        let indentLevel = 0;
                        let hasError = false;
                        
                        for (let i = 0; i < lines.length; i++) {
                            const line = lines[i];
                            if (line.trim() === '') continue;
                            
                            // Basic indentation check
                            const currentIndent = (line.match(/^\\s*/) || [''])[0].length;
                            
                            // Check for unmatched parentheses, brackets, braces
                            const openCount = (line.match(/[\\(\\[\\{]/g) || []).length;
                            const closeCount = (line.match(/[\\)\\]\\}]/g) || []).length;
                            
                            if (line.trim().endsWith(':')) {
                                indentLevel = currentIndent + 4;
                            }
                        }
                        
                        statusElement.textContent = 'Syntax appears valid';
                    } catch (e) {
                        statusElement.textContent = 'Syntax check failed: ' + e.message;
                    }
                }
                
                function insertTemplate() {
                    const templates = [
                        '# Basic template\\nresult = "Hello World"\\nprint(result)',
                        '# Array processing\\nif input_array:\\n    result = f"Array has {len(input_array)} items"\\nelse:\\n    result = "No array provided"',
                        '# Data processing\\nimport json\\ndata = json.loads(input_data) if input_data else {}\\nresult = f"Processed {len(data)} items"',
                        '# Mathematical operations\\nimport math\\nresult = math.sqrt(input_number) if input_number > 0 else 0'
                    ];
                    
                    const choice = prompt('Choose template:\\n0: Basic\\n1: Array processing\\n2: Data processing\\n3: Math operations');
                    if (choice !== null && choice >= 0 && choice < templates.length) {
                        editor.value = templates[parseInt(choice)];
                        updatePosition();
                    }
                }
                
                // Focus the editor
                editor.focus();
                updatePosition();
            </script>
        </body>
        </html>
    `);

    // Listen for messages from the editor window
    window.addEventListener('message', function (event) {
        if (event.data.type === 'pythonCodeEditor') {
            callback(event.data.code);
        }
    });
}

// Initialize the extension
loadCSS();

// Register the extension
app.registerExtension({
    name: "ComfyUI.PythonCodeEditor",

    async beforeRegisterNodeDef(nodeType, nodeData, app) {
        if (nodeData.name === "PythonCodeEditor" || nodeData.name === "PythonCodeEditorAdvanced") {
            // Override the widget creation for python_code input
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function () {
                const result = onNodeCreated?.apply(this, arguments);

                // Find the python_code widget
                const codeWidget = this.widgets?.find(w => w.name === "python_code");
                if (codeWidget) {
                    // Enhance the widget
                    codeWidget.type = "python_code_editor";

                    // Add external editor button for advanced version
                    if (nodeData.name === "PythonCodeEditorAdvanced") {
                        // Create a custom button widget
                        const buttonWidget = createButtonWidget(
                            this,
                            "open_editor_button",
                            "🚀 Open External Editor",
                            () => {
                                const currentCode = codeWidget ? codeWidget.value : "";
                                openExternalEditor(currentCode, (newCode) => {
                                    if (codeWidget) {
                                        codeWidget.value = newCode;
                                        // Trigger the callback to update the node
                                        if (codeWidget.callback) {
                                            codeWidget.callback(newCode);
                                        }
                                    }
                                });
                            }
                        );

                        // Add the button widget to the node
                        this.widgets.push(buttonWidget);
                        this.setSize(this.computeSize());
                    }
                }

                return result;
            };
        }
    }
});

console.log("Python Code Editor extension loaded");
