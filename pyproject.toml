[project]
name = "comfyui_common_utility"
version = "1.0.0"
description = "Common utility nodes for ComfyUI including file operations, string manipulation, and list processing"
authors = [
    {name = "ComfyUI Common Utility", email = ""}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.7"
keywords = ["comfyui", "utility", "file-operations", "string-processing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

[project.urls]
Homepage = "https://github.com/comfyui/comfyui_common_utility"
Repository = "https://github.com/comfyui/comfyui_common_utility"
Issues = "https://github.com/comfyui/comfyui_common_utility/issues"

[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["comfyui_common_utility"]
