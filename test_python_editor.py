#!/usr/bin/env python3
"""
Test script for the Python Code Editor node
"""

import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from logic_operations import Python<PERSON>ode<PERSON>ditor

def test_basic_execution():
    """Test basic Python code execution"""
    print("Testing basic Python code execution...")
    
    editor = PythonCodeEditor()
    
    # Test simple code
    code = """
result = 2 + 2
print(f"2 + 2 = {result}")
"""
    
    output, result_value, success, execution_info = editor.execute_python_code(
        python_code=code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result"
    )
    
    print(f"Output: {output}")
    print(f"Result Value: {result_value}")
    print(f"Success: {success}")
    print(f"Execution Info: {execution_info}")
    print("-" * 50)

def test_syntax_check():
    """Test syntax checking functionality"""
    print("Testing syntax check...")
    
    editor = PythonCodeEditor()
    
    # Test valid syntax
    valid_code = "x = 1 + 2\nprint(x)"
    output, result_value, success, execution_info = editor.execute_python_code(
        python_code=valid_code,
        execution_mode="syntax_check",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result"
    )
    
    print(f"Valid code syntax check - Success: {success}, Info: {execution_info}")
    
    # Test invalid syntax
    invalid_code = "x = 1 + \nprint(x"
    output, result_value, success, execution_info = editor.execute_python_code(
        python_code=invalid_code,
        execution_mode="syntax_check",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result"
    )
    
    print(f"Invalid code syntax check - Success: {success}, Info: {execution_info}")
    print("-" * 50)

def test_with_input_data():
    """Test code execution with input data"""
    print("Testing code execution with input data...")
    
    editor = PythonCodeEditor()
    
    code = """
import json

# Process the input data
data = json.loads(input_data)
result = f"Hello {data['name']}, you are {data['age']} years old!"
print(result)
"""
    
    input_data = '{"name": "Alice", "age": 25}'
    
    output, result_value, success, execution_info = editor.execute_python_code(
        python_code=code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        input_data=input_data
    )
    
    print(f"Output: {output}")
    print(f"Result Value: {result_value}")
    print(f"Success: {success}")
    print(f"Execution Info: {execution_info}")
    print("-" * 50)

def test_complex_computation():
    """Test more complex computation"""
    print("Testing complex computation...")
    
    editor = PythonCodeEditor()
    
    code = """
import math

# Calculate fibonacci sequence
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Calculate first 10 fibonacci numbers
fib_numbers = [fibonacci(i) for i in range(10)]
result = f"First 10 Fibonacci numbers: {fib_numbers}"
print(result)

# Calculate some math
pi_approx = sum(1/16**k * (4/(8*k+1) - 2/(8*k+4) - 1/(8*k+5) - 1/(8*k+6)) for k in range(100))
print(f"Pi approximation: {pi_approx}")
"""
    
    output, result_value, success, execution_info = editor.execute_python_code(
        python_code=code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result"
    )
    
    print(f"Output: {output}")
    print(f"Result Value: {result_value}")
    print(f"Success: {success}")
    print(f"Execution Info: {execution_info}")
    print("-" * 50)

def test_error_handling():
    """Test error handling"""
    print("Testing error handling...")
    
    editor = PythonCodeEditor()
    
    # Test code that raises an exception
    error_code = """
result = 1 / 0  # This will cause a ZeroDivisionError
"""
    
    output, result_value, success, execution_info = editor.execute_python_code(
        python_code=error_code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result"
    )
    
    print(f"Error test - Output: {output}")
    print(f"Error test - Result Value: {result_value}")
    print(f"Error test - Success: {success}")
    print(f"Error test - Execution Info: {execution_info}")
    print("-" * 50)

if __name__ == "__main__":
    print("Starting Python Code Editor Node Tests")
    print("=" * 60)
    
    test_basic_execution()
    test_syntax_check()
    test_with_input_data()
    test_complex_computation()
    test_error_handling()
    
    print("All tests completed!")
