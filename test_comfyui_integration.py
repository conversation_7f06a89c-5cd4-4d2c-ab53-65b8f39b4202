#!/usr/bin/env python3
"""
Test ComfyUI integration for Python Code Editor Node
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_node_registration():
    """Test that the node is properly registered"""
    print("Testing node registration...")
    
    try:
        # Import the mappings directly
        from logic_operations import LOGIC_NODE_CLASS_MAPPINGS, LOGIC_NODE_DISPLAY_NAME_MAPPINGS
        
        # Check if PythonCodeEditor is in the mappings
        if "PythonCodeEditor" in LOGIC_NODE_CLASS_MAPPINGS:
            print("✓ PythonCodeEditor found in LOGIC_NODE_CLASS_MAPPINGS")
        else:
            print("✗ PythonCodeEditor NOT found in LOGIC_NODE_CLASS_MAPPINGS")
            return False
            
        if "PythonCodeEditor" in LOGIC_NODE_DISPLAY_NAME_MAPPINGS:
            print("✓ PythonCodeEditor found in LOGIC_NODE_DISPLAY_NAME_MAPPINGS")
            display_name = LOGIC_NODE_DISPLAY_NAME_MAPPINGS["PythonCodeEditor"]
            print(f"  Display name: {display_name}")
        else:
            print("✗ PythonCodeEditor NOT found in LOGIC_NODE_DISPLAY_NAME_MAPPINGS")
            return False
            
        # Test node instantiation
        PythonCodeEditor = LOGIC_NODE_CLASS_MAPPINGS["PythonCodeEditor"]
        node_instance = PythonCodeEditor()
        print("✓ PythonCodeEditor node instantiated successfully")
        
        # Test INPUT_TYPES method
        input_types = PythonCodeEditor.INPUT_TYPES()
        print("✓ INPUT_TYPES method works")
        print(f"  Required inputs: {list(input_types['required'].keys())}")
        print(f"  Optional inputs: {list(input_types.get('optional', {}).keys())}")
        
        # Test node attributes
        print(f"✓ RETURN_TYPES: {PythonCodeEditor.RETURN_TYPES}")
        print(f"✓ RETURN_NAMES: {PythonCodeEditor.RETURN_NAMES}")
        print(f"✓ FUNCTION: {PythonCodeEditor.FUNCTION}")
        print(f"✓ CATEGORY: {PythonCodeEditor.CATEGORY}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during node registration test: {e}")
        return False

def test_node_functionality():
    """Test basic node functionality"""
    print("\nTesting node functionality...")
    
    try:
        from logic_operations import PythonCodeEditor
        
        node = PythonCodeEditor()
        
        # Test simple execution
        code = "result = 'Hello from Python Code Editor!'"
        output, result_value, success, execution_info = node.execute_python_code(
            python_code=code,
            execution_mode="execute",
            timeout_seconds=10,
            capture_output=True,
            return_variable="result"
        )
        
        if success:
            print("✓ Basic code execution works")
            print(f"  Result: {result_value}")
        else:
            print("✗ Basic code execution failed")
            print(f"  Error: {execution_info}")
            return False
            
        # Test syntax check
        output, result_value, success, execution_info = node.execute_python_code(
            python_code="x = 1 + 2",
            execution_mode="syntax_check",
            timeout_seconds=10,
            capture_output=True,
            return_variable="result"
        )
        
        if success:
            print("✓ Syntax check works")
        else:
            print("✗ Syntax check failed")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Error during functionality test: {e}")
        return False

def simulate_comfyui_environment():
    """Simulate how ComfyUI would load the nodes"""
    print("\nSimulating ComfyUI environment...")
    
    try:
        # Simulate the way ComfyUI imports custom nodes
        import file_operations
        import utility_operations
        import logic_operations
        
        # Combine mappings like __init__.py does
        NODE_CLASS_MAPPINGS = {
            **file_operations.NODE_CLASS_MAPPINGS,
            **utility_operations.UTILITY_NODE_CLASS_MAPPINGS,
            **logic_operations.LOGIC_NODE_CLASS_MAPPINGS
        }
        
        NODE_DISPLAY_NAME_MAPPINGS = {
            **file_operations.NODE_DISPLAY_NAME_MAPPINGS,
            **utility_operations.UTILITY_NODE_DISPLAY_NAME_MAPPINGS,
            **logic_operations.LOGIC_NODE_DISPLAY_NAME_MAPPINGS
        }
        
        print(f"✓ Total nodes available: {len(NODE_CLASS_MAPPINGS)}")
        
        if "PythonCodeEditor" in NODE_CLASS_MAPPINGS:
            print("✓ PythonCodeEditor available in combined mappings")
            
            # Test the node as ComfyUI would
            PythonCodeEditor = NODE_CLASS_MAPPINGS["PythonCodeEditor"]
            display_name = NODE_DISPLAY_NAME_MAPPINGS["PythonCodeEditor"]
            
            print(f"  Node class: {PythonCodeEditor}")
            print(f"  Display name: {display_name}")
            
            # Test instantiation and basic method call
            node = PythonCodeEditor()
            input_types = node.INPUT_TYPES()
            print(f"  Input types configured: {len(input_types['required'])} required, {len(input_types.get('optional', {}))} optional")
            
            return True
        else:
            print("✗ PythonCodeEditor NOT found in combined mappings")
            return False
            
    except Exception as e:
        print(f"✗ Error simulating ComfyUI environment: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("ComfyUI Integration Test for Python Code Editor Node")
    print("=" * 60)
    
    success = True
    
    success &= test_node_registration()
    success &= test_node_functionality()
    success &= simulate_comfyui_environment()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！Python代码编辑器节点已准备好在ComfyUI中使用。")
        print("\n使用方法:")
        print("1. 重启ComfyUI")
        print("2. 在节点菜单中找到 'Common Utility/Code Operations/Python Code Editor'")
        print("3. 添加节点到工作流")
        print("4. 在python_code输入框中编写Python代码")
        print("5. 连接输入和输出，运行工作流")
    else:
        print("❌ 某些测试失败，请检查错误信息。")
