#!/usr/bin/env python3
"""
测试真正的按钮实现
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from logic_operations import PythonCodeEditorAdvanced

def test_node_parameters():
    """测试节点参数配置"""
    print("🔧 测试节点参数配置")
    print("=" * 50)
    
    editor = PythonCodeEditorAdvanced()
    
    # 检查INPUT_TYPES
    input_types = editor.INPUT_TYPES()
    
    print("📋 必需参数:")
    required_params = list(input_types['required'].keys())
    for param in required_params:
        print(f"  ✓ {param}")
    
    print("\n📋 可选参数:")
    optional_params = list(input_types.get('optional', {}).keys())
    for param in optional_params:
        print(f"  ✓ {param}")
    
    print("\n📋 隐藏参数:")
    hidden_params = list(input_types.get('hidden', {}).keys())
    for param in hidden_params:
        print(f"  ✓ {param}")
    
    # 检查是否移除了open_external_editor参数
    if 'open_external_editor' not in required_params:
        print("\n✅ open_external_editor 参数已成功移除")
    else:
        print("\n❌ open_external_editor 参数仍然存在")
    
    print()

def test_node_execution():
    """测试节点执行功能"""
    print("🧪 测试节点执行功能")
    print("=" * 50)
    
    editor = PythonCodeEditorAdvanced()
    
    # 测试代码
    test_code = """
# 真正按钮实现测试
print("🔘 真正的按钮实现测试")

# 处理输入
print(f"输入数据: {input_data}")
print(f"输入数组: {input_array}")
print(f"输入数字: {input_number}")

# 生成结果
result = {
    "implementation": "real_button_widget",
    "button_type": "addWidget('button')",
    "status": "working",
    "features": [
        "Real button widget",
        "No toggle behavior", 
        "JavaScript addWidget API",
        "Immediate response"
    ]
}

print(f"\\n✅ 结果: {result}")
"""
    
    # 执行测试（不需要按钮参数）
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=test_code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        unique_id="real_button_test",
        input_data="真正按钮测试数据",
        input_array=["真正", "按钮", "实现", 456],
        input_number=77.7,
        input_image=None
    )
    
    print(f"📤 输出:\n{output}")
    print(f"🎯 结果值: {result_value}")
    print(f"📦 结果对象: {result_object}")
    print(f"✅ 成功: {success}")
    print(f"ℹ️ 执行信息: {execution_info}")
    print()

def check_javascript_implementation():
    """检查JavaScript实现"""
    print("📁 检查JavaScript实现")
    print("=" * 50)
    
    js_file = "web/extensions/python_code_editor.js"
    
    if os.path.exists(js_file):
        print(f"✅ JavaScript文件存在: {js_file}")
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键实现
        if "addWidget" in content:
            print("✅ addWidget 方法存在")
        else:
            print("❌ addWidget 方法不存在")
            
        if '"button"' in content:
            print("✅ button 类型存在")
        else:
            print("❌ button 类型不存在")
            
        if "🚀 Open External Editor" in content:
            print("✅ 按钮文本存在")
        else:
            print("❌ 按钮文本不存在")
            
        if "splice" in content:
            print("✅ toggle移除逻辑存在")
        else:
            print("❌ toggle移除逻辑不存在")
            
        if "serialize = false" in content:
            print("✅ 按钮序列化禁用存在")
        else:
            print("❌ 按钮序列化禁用不存在")
            
    else:
        print(f"❌ JavaScript文件不存在: {js_file}")
    
    print()

def show_implementation_details():
    """显示实现细节"""
    print("🔧 实现细节")
    print("=" * 50)
    
    print("📋 真正按钮实现方式:")
    print("  ✓ 使用 node.addWidget('button') API")
    print("  ✓ 移除原有的BOOLEAN toggle参数")
    print("  ✓ 纯JavaScript实现，无需Python参数")
    print("  ✓ 设置 serialize = false 避免保存状态")
    
    print("\n🎯 JavaScript实现:")
    print("  1. 检测到PythonCodeEditorAdvanced节点")
    print("  2. 移除open_external_editor toggle widget")
    print("  3. 添加真正的button widget")
    print("  4. 设置按钮回调函数")
    print("  5. 禁用按钮状态序列化")
    
    print("\n🔄 按钮行为:")
    print("  - 显示: '🚀 Open External Editor'")
    print("  - 点击: 立即执行回调函数")
    print("  - 响应: 打开外部编辑器窗口")
    print("  - 状态: 不保存，不序列化")
    
    print("\n📁 修改的文件:")
    print("  ✓ logic_operations.py - 移除open_external_editor参数")
    print("  ✓ web/extensions/python_code_editor.js - 实现真正按钮")
    print("  ✓ 保持CSS样式文件不变")
    
    print()

def show_usage_instructions():
    """显示使用说明"""
    print("📝 使用说明")
    print("=" * 50)
    
    print("🚀 如何使用:")
    print("  1. 重启ComfyUI")
    print("  2. 添加 'Python Code Editor (Advanced)' 节点")
    print("  3. 在节点中找到真正的按钮 '🚀 Open External Editor'")
    print("  4. 点击按钮立即打开外部编辑器")
    print("  5. 在编辑器中编写代码")
    print("  6. 按Ctrl+S保存并关闭")
    print("  7. 代码自动同步到节点")
    print("  8. 运行工作流测试代码")
    
    print("\n🔍 按钮特征:")
    print("  ✓ 真正的按钮，不是toggle开关")
    print("  ✓ 点击即响应，无需运行工作流")
    print("  ✓ 不保存状态，每次都是新的")
    print("  ✓ 使用ComfyUI原生按钮API")
    
    print("\n🔧 技术特点:")
    print("  ✓ 使用 addWidget('button') API")
    print("  ✓ 纯前端实现，无后端参数")
    print("  ✓ 自动移除toggle widget")
    print("  ✓ 禁用状态序列化")
    
    print("\n🔍 故障排除:")
    print("  - 如果仍然看到toggle，清除浏览器缓存")
    print("  - 确保完全重启ComfyUI")
    print("  - 检查浏览器控制台错误")
    print("  - 确认JavaScript文件正确加载")
    
    print()

if __name__ == "__main__":
    print("🔘 真正按钮实现测试")
    print("=" * 60)
    print()
    
    test_node_parameters()
    test_node_execution()
    check_javascript_implementation()
    show_implementation_details()
    show_usage_instructions()
    
    print("🎊 测试完成!")
    print("\n📋 总结:")
    print("✅ 移除了BOOLEAN toggle参数")
    print("🔘 实现了真正的按钮widget")
    print("🎯 使用ComfyUI原生addWidget API")
    print("⚡ 即时响应，无状态保存")
    print("📦 完整功能保持")
    print("\n🚀 现在应该是真正的按钮了! 重启ComfyUI并测试。")
