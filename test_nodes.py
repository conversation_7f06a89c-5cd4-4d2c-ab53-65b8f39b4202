#!/usr/bin/env python3
"""
Simple test script to verify that the ComfyUI Common Utility nodes work correctly.
This can be run independently of ComfyUI for basic testing.
"""

import os
import sys
import tempfile
import shutil

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(__file__))

from file_operations import *
from utility_operations import *
from logic_operations import *


def test_file_operations():
    """Test file operation nodes"""
    print("Testing File Operations...")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = os.path.join(temp_dir, "test.txt")
        test_content = "Hello, ComfyUI Common Utility!"
        
        # Test WriteTextFile
        write_node = WriteTextFile()
        result, success = write_node.write_text_file(test_file, test_content, "utf-8", True, True)
        print(f"WriteTextFile: {result} (Success: {success})")
        
        # Test FileExists
        exists_node = FileExists()
        exists, file_type, message = exists_node.file_exists(test_file)
        print(f"FileExists: {exists}, Type: {file_type}, Message: {message}")
        
        # Test ReadTextFile
        read_node = ReadTextFile()
        content, message, success = read_node.read_text_file(test_file, "utf-8")
        print(f"ReadTextFile: Content: '{content}', Success: {success}")
        
        # Test GetFileInfo
        info_node = GetFileInfo()
        info, size, file_type, message, success = info_node.get_file_info(test_file)
        print(f"GetFileInfo: Size: {size}, Type: {file_type}, Success: {success}")
        
        # Test CopyFile
        copy_dest = os.path.join(temp_dir, "test_copy.txt")
        copy_node = CopyFile()
        result, success = copy_node.copy_file(test_file, copy_dest, True, True)
        print(f"CopyFile: {result} (Success: {success})")
        
        # Test ListDirectory
        list_node = ListDirectory()
        file_list, message, count = list_node.list_directory(temp_dir, True, True, False, "*")
        print(f"ListDirectory: Found {count} items")
        print(f"Files: {file_list}")


def test_string_operations():
    """Test string operation nodes"""
    print("\nTesting String Operations...")
    
    # Test StringSplit
    split_node = StringSplit()
    text = "apple,banana,cherry,date"
    split_list, count, message = split_node.split_string(text, ",", True, True)
    print(f"StringSplit: Split '{text}' into {count} parts")
    
    # Test StringJoin
    join_node = StringJoin()
    joined, message = join_node.join_strings(split_list, " | ", "\n")
    print(f"StringJoin: Joined result: '{joined}'")
    
    # Test StringReplace
    replace_node = StringReplace()
    result, replacements, message = replace_node.replace_string(text, "banana", "BANANA", False, True, True)
    print(f"StringReplace: '{result}' ({replacements} replacements)")


def test_list_operations():
    """Test list operation nodes"""
    print("\nTesting List Operations...")
    
    test_list = "apple\nbanana\ncherry\napricot\nblueberry"
    
    # Test ListFilter
    filter_node = ListFilter()
    filtered, count, message = filter_node.filter_list(test_list, "starts_with", "a", True, False, "\n")
    print(f"ListFilter: Filtered to {count} items starting with 'a'")
    print(f"Filtered items: {filtered}")
    
    # Test ListSort
    sort_node = ListSort()
    sorted_list, count, message = sort_node.sort_list(test_list, "ascending", "alphabetical", True, "\n")
    print(f"ListSort: Sorted {count} items alphabetically")
    print(f"Sorted items: {sorted_list}")
    
    # Test RandomChoice
    random_node = RandomChoice()
    selected, single, message = random_node.random_choice(test_list, 2, False, 42, "\n")
    print(f"RandomChoice: {message}")
    print(f"Selected items: {selected}")


def test_logic_operations():
    """Test logic operation nodes"""
    print("\nTesting Logic Operations...")
    
    # Test ConditionalString
    conditional_node = ConditionalString()
    result, message = conditional_node.conditional_string(True, "It's true!", "It's false!")
    print(f"ConditionalString: {result}")
    
    # Test StringComparison
    comparison_node = StringComparison()
    result, message = comparison_node.compare_strings("hello", "HELLO", "equals", False)
    print(f"StringComparison: {message}")
    
    # Test NumberComparison
    number_comp_node = NumberComparison()
    result, message = number_comp_node.compare_numbers(3.14159, 3.14, "equals", 0.01)
    print(f"NumberComparison: {message}")
    
    # Test LogicalOperations
    logical_node = LogicalOperations()
    result, message = logical_node.logical_operation(True, False, "OR")
    print(f"LogicalOperations: {message}")
    
    # Test CounterNode
    counter_node = CounterNode()
    count, message = counter_node.count(False, 1, 0)
    print(f"CounterNode: {message}")
    count, message = counter_node.count(False, 5, 0)
    print(f"CounterNode: {message}")
    
    # Test JSONProcessor
    json_node = JSONProcessor()
    test_json = '{"name": "test", "values": [1, 2, 3], "nested": {"key": "value"}}'
    result, success, message = json_node.process_json(test_json, "extract_key", "nested.key", True)
    print(f"JSONProcessor: Extracted value: '{result}' (Success: {success})")


def main():
    """Run all tests"""
    print("ComfyUI Common Utility - Node Testing")
    print("=" * 50)
    
    try:
        test_file_operations()
        test_string_operations()
        test_list_operations()
        test_logic_operations()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"\nError during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
