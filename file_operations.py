import os
import shutil
import json
import glob
from pathlib import Path
from typing import List, Tuple, Any


class FileOperationBase:
    """Base class for file operations with common utilities"""
    
    @staticmethod
    def safe_path(path: str) -> str:
        """Ensure path is safe and normalized"""
        return os.path.normpath(os.path.abspath(path))
    
    @staticmethod
    def ensure_directory_exists(path: str) -> bool:
        """Create directory if it doesn't exist"""
        try:
            os.makedirs(path, exist_ok=True)
            return True
        except Exception as e:
            print(f"Error creating directory {path}: {e}")
            return False


class CopyFile(FileOperationBase):
    """Copy a file from source to destination"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "source_path": ("STRING", {"default": "", "multiline": False}),
                "destination_path": ("STRING", {"default": "", "multiline": False}),
                "create_dirs": ("BOOLEAN", {"default": True}),
                "overwrite": ("BOOLEAN", {"default": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "BOOLEAN")
    RETURN_NAMES = ("result_message", "success")
    FUNCTION = "copy_file"
    CATEGORY = "Common Utility/File Operations"
    
    def copy_file(self, source_path: str, destination_path: str, create_dirs: bool, overwrite: bool):
        try:
            source = self.safe_path(source_path)
            destination = self.safe_path(destination_path)
            
            if not os.path.exists(source):
                return (f"Source file does not exist: {source}", False)
            
            if os.path.exists(destination) and not overwrite:
                return (f"Destination file already exists: {destination}", False)
            
            if create_dirs:
                dest_dir = os.path.dirname(destination)
                if not self.ensure_directory_exists(dest_dir):
                    return (f"Failed to create destination directory: {dest_dir}", False)
            
            shutil.copy2(source, destination)
            return (f"Successfully copied {source} to {destination}", True)
            
        except Exception as e:
            return (f"Error copying file: {str(e)}", False)


class MoveFile(FileOperationBase):
    """Move/rename a file from source to destination"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "source_path": ("STRING", {"default": "", "multiline": False}),
                "destination_path": ("STRING", {"default": "", "multiline": False}),
                "create_dirs": ("BOOLEAN", {"default": True}),
                "overwrite": ("BOOLEAN", {"default": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "BOOLEAN")
    RETURN_NAMES = ("result_message", "success")
    FUNCTION = "move_file"
    CATEGORY = "Common Utility/File Operations"
    
    def move_file(self, source_path: str, destination_path: str, create_dirs: bool, overwrite: bool):
        try:
            source = self.safe_path(source_path)
            destination = self.safe_path(destination_path)
            
            if not os.path.exists(source):
                return (f"Source file does not exist: {source}", False)
            
            if os.path.exists(destination) and not overwrite:
                return (f"Destination file already exists: {destination}", False)
            
            if create_dirs:
                dest_dir = os.path.dirname(destination)
                if not self.ensure_directory_exists(dest_dir):
                    return (f"Failed to create destination directory: {dest_dir}", False)
            
            shutil.move(source, destination)
            return (f"Successfully moved {source} to {destination}", True)
            
        except Exception as e:
            return (f"Error moving file: {str(e)}", False)


class DeleteFile(FileOperationBase):
    """Delete a file"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "file_path": ("STRING", {"default": "", "multiline": False}),
                "confirm_delete": ("BOOLEAN", {"default": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "BOOLEAN")
    RETURN_NAMES = ("result_message", "success")
    FUNCTION = "delete_file"
    CATEGORY = "Common Utility/File Operations"
    
    def delete_file(self, file_path: str, confirm_delete: bool):
        try:
            if not confirm_delete:
                return ("Delete operation cancelled - confirm_delete must be True", False)
            
            file_path = self.safe_path(file_path)
            
            if not os.path.exists(file_path):
                return (f"File does not exist: {file_path}", False)
            
            if os.path.isdir(file_path):
                return (f"Path is a directory, not a file: {file_path}", False)
            
            os.remove(file_path)
            return (f"Successfully deleted file: {file_path}", True)
            
        except Exception as e:
            return (f"Error deleting file: {str(e)}", False)


class ListDirectory(FileOperationBase):
    """List files and directories in a given path"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "directory_path": ("STRING", {"default": "", "multiline": False}),
                "include_files": ("BOOLEAN", {"default": True}),
                "include_dirs": ("BOOLEAN", {"default": True}),
                "recursive": ("BOOLEAN", {"default": False}),
                "file_pattern": ("STRING", {"default": "*", "multiline": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING", "INT")
    RETURN_NAMES = ("file_list", "result_message", "count")
    FUNCTION = "list_directory"
    CATEGORY = "Common Utility/File Operations"
    
    def list_directory(self, directory_path: str, include_files: bool, include_dirs: bool, 
                      recursive: bool, file_pattern: str):
        try:
            directory_path = self.safe_path(directory_path)
            
            if not os.path.exists(directory_path):
                return ("", f"Directory does not exist: {directory_path}", 0)
            
            if not os.path.isdir(directory_path):
                return ("", f"Path is not a directory: {directory_path}", 0)
            
            items = []
            
            if recursive:
                pattern_path = os.path.join(directory_path, "**", file_pattern)
                all_items = glob.glob(pattern_path, recursive=True)
            else:
                pattern_path = os.path.join(directory_path, file_pattern)
                all_items = glob.glob(pattern_path)
            
            for item in all_items:
                if os.path.isfile(item) and include_files:
                    items.append(f"FILE: {item}")
                elif os.path.isdir(item) and include_dirs:
                    items.append(f"DIR: {item}")
            
            items.sort()
            file_list = "\n".join(items)
            count = len(items)
            
            return (file_list, f"Found {count} items in {directory_path}", count)
            
        except Exception as e:
            return ("", f"Error listing directory: {str(e)}", 0)


class CreateDirectory(FileOperationBase):
    """Create a new directory"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "directory_path": ("STRING", {"default": "", "multiline": False}),
                "create_parents": ("BOOLEAN", {"default": True})
            }
        }
    
    RETURN_TYPES = ("STRING", "BOOLEAN")
    RETURN_NAMES = ("result_message", "success")
    FUNCTION = "create_directory"
    CATEGORY = "Common Utility/File Operations"
    
    def create_directory(self, directory_path: str, create_parents: bool):
        try:
            directory_path = self.safe_path(directory_path)
            
            if os.path.exists(directory_path):
                if os.path.isdir(directory_path):
                    return (f"Directory already exists: {directory_path}", True)
                else:
                    return (f"Path exists but is not a directory: {directory_path}", False)
            
            if create_parents:
                os.makedirs(directory_path, exist_ok=True)
            else:
                os.mkdir(directory_path)
            
            return (f"Successfully created directory: {directory_path}", True)
            
        except Exception as e:
            return (f"Error creating directory: {str(e)}", False)


class FileExists(FileOperationBase):
    """Check if a file or directory exists"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "file_path": ("STRING", {"default": "", "multiline": False})
            }
        }
    
    RETURN_TYPES = ("BOOLEAN", "STRING", "STRING")
    RETURN_NAMES = ("exists", "file_type", "result_message")
    FUNCTION = "file_exists"
    CATEGORY = "Common Utility/File Operations"
    
    def file_exists(self, file_path: str):
        try:
            file_path = self.safe_path(file_path)
            
            if not os.path.exists(file_path):
                return (False, "none", f"Path does not exist: {file_path}")
            
            if os.path.isfile(file_path):
                return (True, "file", f"File exists: {file_path}")
            elif os.path.isdir(file_path):
                return (True, "directory", f"Directory exists: {file_path}")
            else:
                return (True, "other", f"Path exists (special file): {file_path}")
                
        except Exception as e:
            return (False, "error", f"Error checking path: {str(e)}")


class ReadTextFile(FileOperationBase):
    """Read content from a text file"""

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "file_path": ("STRING", {"default": "", "multiline": False}),
                "encoding": (["utf-8", "utf-16", "ascii", "latin-1"], {"default": "utf-8"})
            }
        }

    RETURN_TYPES = ("STRING", "STRING", "BOOLEAN")
    RETURN_NAMES = ("file_content", "result_message", "success")
    FUNCTION = "read_text_file"
    CATEGORY = "Common Utility/File Operations"

    def read_text_file(self, file_path: str, encoding: str):
        try:
            file_path = self.safe_path(file_path)

            if not os.path.exists(file_path):
                return ("", f"File does not exist: {file_path}", False)

            if not os.path.isfile(file_path):
                return ("", f"Path is not a file: {file_path}", False)

            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()

            return (content, f"Successfully read file: {file_path}", True)

        except Exception as e:
            return ("", f"Error reading file: {str(e)}", False)


class WriteTextFile(FileOperationBase):
    """Write content to a text file"""

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "file_path": ("STRING", {"default": "", "multiline": False}),
                "content": ("STRING", {"default": "", "multiline": True}),
                "encoding": (["utf-8", "utf-16", "ascii", "latin-1"], {"default": "utf-8"}),
                "create_dirs": ("BOOLEAN", {"default": True}),
                "overwrite": ("BOOLEAN", {"default": False})
            }
        }

    RETURN_TYPES = ("STRING", "BOOLEAN")
    RETURN_NAMES = ("result_message", "success")
    FUNCTION = "write_text_file"
    CATEGORY = "Common Utility/File Operations"

    def write_text_file(self, file_path: str, content: str, encoding: str, create_dirs: bool, overwrite: bool):
        try:
            file_path = self.safe_path(file_path)

            if os.path.exists(file_path) and not overwrite:
                return (f"File already exists: {file_path}", False)

            if create_dirs:
                dir_path = os.path.dirname(file_path)
                if not self.ensure_directory_exists(dir_path):
                    return (f"Failed to create directory: {dir_path}", False)

            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)

            return (f"Successfully wrote to file: {file_path}", True)

        except Exception as e:
            return (f"Error writing file: {str(e)}", False)


class GetFileInfo(FileOperationBase):
    """Get detailed information about a file or directory"""

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "file_path": ("STRING", {"default": "", "multiline": False})
            }
        }

    RETURN_TYPES = ("STRING", "INT", "STRING", "STRING", "BOOLEAN")
    RETURN_NAMES = ("file_info", "file_size", "file_type", "result_message", "success")
    FUNCTION = "get_file_info"
    CATEGORY = "Common Utility/File Operations"

    def get_file_info(self, file_path: str):
        try:
            file_path = self.safe_path(file_path)

            if not os.path.exists(file_path):
                return ("", 0, "none", f"Path does not exist: {file_path}", False)

            stat = os.stat(file_path)

            import time
            created_time = time.ctime(stat.st_ctime)
            modified_time = time.ctime(stat.st_mtime)
            accessed_time = time.ctime(stat.st_atime)

            if os.path.isfile(file_path):
                file_type = "file"
                size = stat.st_size
            elif os.path.isdir(file_path):
                file_type = "directory"
                size = 0  # Directory size calculation would be expensive
            else:
                file_type = "other"
                size = stat.st_size

            info = f"""Path: {file_path}
Type: {file_type}
Size: {size} bytes
Created: {created_time}
Modified: {modified_time}
Accessed: {accessed_time}
Permissions: {oct(stat.st_mode)[-3:]}"""

            return (info, size, file_type, f"Successfully retrieved info for: {file_path}", True)

        except Exception as e:
            return ("", 0, "error", f"Error getting file info: {str(e)}", False)


# Node mappings
NODE_CLASS_MAPPINGS = {
    "CopyFile": CopyFile,
    "MoveFile": MoveFile,
    "DeleteFile": DeleteFile,
    "ListDirectory": ListDirectory,
    "CreateDirectory": CreateDirectory,
    "FileExists": FileExists,
    "ReadTextFile": ReadTextFile,
    "WriteTextFile": WriteTextFile,
    "GetFileInfo": GetFileInfo,
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "CopyFile": "Copy File",
    "MoveFile": "Move/Rename File",
    "DeleteFile": "Delete File",
    "ListDirectory": "List Directory",
    "CreateDirectory": "Create Directory",
    "FileExists": "File/Directory Exists",
    "ReadTextFile": "Read Text File",
    "WriteTextFile": "Write Text File",
    "GetFileInfo": "Get File Info",
}
