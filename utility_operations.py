import re
import json
import random
from typing import List, Any


class StringSplit:
    """Split a string into a list using a delimiter"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "text": ("STRING", {"default": "", "multiline": True}),
                "delimiter": ("STRING", {"default": "\n", "multiline": False}),
                "remove_empty": ("BOOLEAN", {"default": True}),
                "strip_whitespace": ("BOOLEAN", {"default": True})
            }
        }
    
    RETURN_TYPES = ("STRING", "INT", "STRING")
    RETURN_NAMES = ("split_list", "count", "result_message")
    FUNCTION = "split_string"
    CATEGORY = "Common Utility/String Operations"
    
    def split_string(self, text: str, delimiter: str, remove_empty: bool, strip_whitespace: bool):
        try:
            parts = text.split(delimiter)
            
            if strip_whitespace:
                parts = [part.strip() for part in parts]
            
            if remove_empty:
                parts = [part for part in parts if part]
            
            result_list = "\n".join(parts)
            count = len(parts)
            
            return (result_list, count, f"Split into {count} parts")
            
        except Exception as e:
            return ("", 0, f"Error splitting string: {str(e)}")


class StringJoin:
    """Join a list of strings with a delimiter"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "string_list": ("STRING", {"default": "", "multiline": True}),
                "delimiter": ("STRING", {"default": "\n", "multiline": False}),
                "input_delimiter": ("STRING", {"default": "\n", "multiline": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("joined_string", "result_message")
    FUNCTION = "join_strings"
    CATEGORY = "Common Utility/String Operations"
    
    def join_strings(self, string_list: str, delimiter: str, input_delimiter: str):
        try:
            parts = string_list.split(input_delimiter)
            parts = [part.strip() for part in parts if part.strip()]
            
            joined = delimiter.join(parts)
            
            return (joined, f"Joined {len(parts)} strings")
            
        except Exception as e:
            return ("", f"Error joining strings: {str(e)}")


class StringReplace:
    """Replace text in a string using regex or simple replacement"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "text": ("STRING", {"default": "", "multiline": True}),
                "search": ("STRING", {"default": "", "multiline": False}),
                "replace": ("STRING", {"default": "", "multiline": False}),
                "use_regex": ("BOOLEAN", {"default": False}),
                "case_sensitive": ("BOOLEAN", {"default": True}),
                "replace_all": ("BOOLEAN", {"default": True})
            }
        }
    
    RETURN_TYPES = ("STRING", "INT", "STRING")
    RETURN_NAMES = ("result_text", "replacements_made", "result_message")
    FUNCTION = "replace_string"
    CATEGORY = "Common Utility/String Operations"
    
    def replace_string(self, text: str, search: str, replace: str, use_regex: bool, 
                      case_sensitive: bool, replace_all: bool):
        try:
            if not search:
                return (text, 0, "No search pattern provided")
            
            if use_regex:
                flags = 0 if case_sensitive else re.IGNORECASE
                count = 0 if replace_all else 1
                
                result, replacements = re.subn(search, replace, text, count=count, flags=flags)
            else:
                if not case_sensitive:
                    # For simple replacement with case insensitive, we need to find all occurrences
                    import re
                    pattern = re.escape(search)
                    result, replacements = re.subn(pattern, replace, text, 
                                                 count=0 if replace_all else 1, 
                                                 flags=re.IGNORECASE)
                else:
                    if replace_all:
                        result = text.replace(search, replace)
                        replacements = text.count(search)
                    else:
                        result = text.replace(search, replace, 1)
                        replacements = 1 if search in text else 0
            
            return (result, replacements, f"Made {replacements} replacements")
            
        except Exception as e:
            return (text, 0, f"Error replacing text: {str(e)}")


class ListFilter:
    """Filter a list based on various criteria"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "string_list": ("STRING", {"default": "", "multiline": True}),
                "filter_type": (["contains", "starts_with", "ends_with", "regex", "length"], {"default": "contains"}),
                "filter_value": ("STRING", {"default": "", "multiline": False}),
                "case_sensitive": ("BOOLEAN", {"default": True}),
                "invert_filter": ("BOOLEAN", {"default": False}),
                "input_delimiter": ("STRING", {"default": "\n", "multiline": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "INT", "STRING")
    RETURN_NAMES = ("filtered_list", "count", "result_message")
    FUNCTION = "filter_list"
    CATEGORY = "Common Utility/List Operations"
    
    def filter_list(self, string_list: str, filter_type: str, filter_value: str, 
                   case_sensitive: bool, invert_filter: bool, input_delimiter: str):
        try:
            items = string_list.split(input_delimiter)
            items = [item.strip() for item in items if item.strip()]
            
            filtered_items = []
            
            for item in items:
                match = False
                
                if filter_type == "contains":
                    search_item = item if case_sensitive else item.lower()
                    search_value = filter_value if case_sensitive else filter_value.lower()
                    match = search_value in search_item
                    
                elif filter_type == "starts_with":
                    search_item = item if case_sensitive else item.lower()
                    search_value = filter_value if case_sensitive else filter_value.lower()
                    match = search_item.startswith(search_value)
                    
                elif filter_type == "ends_with":
                    search_item = item if case_sensitive else item.lower()
                    search_value = filter_value if case_sensitive else filter_value.lower()
                    match = search_item.endswith(search_value)
                    
                elif filter_type == "regex":
                    flags = 0 if case_sensitive else re.IGNORECASE
                    match = bool(re.search(filter_value, item, flags))
                    
                elif filter_type == "length":
                    try:
                        target_length = int(filter_value)
                        match = len(item) == target_length
                    except ValueError:
                        match = False
                
                if invert_filter:
                    match = not match
                
                if match:
                    filtered_items.append(item)
            
            result_list = "\n".join(filtered_items)
            count = len(filtered_items)
            
            return (result_list, count, f"Filtered to {count} items")
            
        except Exception as e:
            return ("", 0, f"Error filtering list: {str(e)}")


class ListSort:
    """Sort a list of strings"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "string_list": ("STRING", {"default": "", "multiline": True}),
                "sort_order": (["ascending", "descending"], {"default": "ascending"}),
                "sort_type": (["alphabetical", "length", "numerical"], {"default": "alphabetical"}),
                "case_sensitive": ("BOOLEAN", {"default": True}),
                "input_delimiter": ("STRING", {"default": "\n", "multiline": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "INT", "STRING")
    RETURN_NAMES = ("sorted_list", "count", "result_message")
    FUNCTION = "sort_list"
    CATEGORY = "Common Utility/List Operations"
    
    def sort_list(self, string_list: str, sort_order: str, sort_type: str, 
                 case_sensitive: bool, input_delimiter: str):
        try:
            items = string_list.split(input_delimiter)
            items = [item.strip() for item in items if item.strip()]
            
            if sort_type == "alphabetical":
                if case_sensitive:
                    items.sort()
                else:
                    items.sort(key=str.lower)
            elif sort_type == "length":
                items.sort(key=len)
            elif sort_type == "numerical":
                def safe_float(x):
                    try:
                        return float(x)
                    except ValueError:
                        return float('inf')  # Put non-numeric items at the end
                items.sort(key=safe_float)
            
            if sort_order == "descending":
                items.reverse()
            
            result_list = "\n".join(items)
            count = len(items)
            
            return (result_list, count, f"Sorted {count} items")
            
        except Exception as e:
            return ("", 0, f"Error sorting list: {str(e)}")


class RandomChoice:
    """Randomly select item(s) from a list"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "string_list": ("STRING", {"default": "", "multiline": True}),
                "count": ("INT", {"default": 1, "min": 1, "max": 100}),
                "allow_duplicates": ("BOOLEAN", {"default": False}),
                "seed": ("INT", {"default": -1, "min": -1, "max": 2147483647}),
                "input_delimiter": ("STRING", {"default": "\n", "multiline": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING", "STRING")
    RETURN_NAMES = ("selected_items", "selected_single", "result_message")
    FUNCTION = "random_choice"
    CATEGORY = "Common Utility/List Operations"
    
    def random_choice(self, string_list: str, count: int, allow_duplicates: bool, 
                     seed: int, input_delimiter: str):
        try:
            items = string_list.split(input_delimiter)
            items = [item.strip() for item in items if item.strip()]
            
            if not items:
                return ("", "", "No items to choose from")
            
            if seed >= 0:
                random.seed(seed)
            
            if allow_duplicates:
                selected = [random.choice(items) for _ in range(count)]
            else:
                if count > len(items):
                    count = len(items)
                selected = random.sample(items, count)
            
            selected_list = "\n".join(selected)
            selected_single = selected[0] if selected else ""
            
            return (selected_list, selected_single, f"Selected {len(selected)} items")
            
        except Exception as e:
            return ("", "", f"Error selecting random items: {str(e)}")


# Node mappings for utility operations
UTILITY_NODE_CLASS_MAPPINGS = {
    "StringSplit": StringSplit,
    "StringJoin": StringJoin,
    "StringReplace": StringReplace,
    "ListFilter": ListFilter,
    "ListSort": ListSort,
    "RandomChoice": RandomChoice,
}

UTILITY_NODE_DISPLAY_NAME_MAPPINGS = {
    "StringSplit": "Split String",
    "StringJoin": "Join Strings",
    "StringReplace": "Replace Text",
    "ListFilter": "Filter List",
    "ListSort": "Sort List",
    "RandomChoice": "Random Choice",
}
