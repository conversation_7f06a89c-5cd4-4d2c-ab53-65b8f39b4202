#!/usr/bin/env python3
"""
Python Code Editor Node - Usage Examples
这个文件包含了一些使用Python代码编辑器节点的示例代码
"""

# 示例1: 基本数学计算
basic_math_example = """
# 基本数学计算示例
import math

# 计算圆的面积和周长
radius = 5
area = math.pi * radius ** 2
circumference = 2 * math.pi * radius

result = f"半径为{radius}的圆：面积={area:.2f}, 周长={circumference:.2f}"
print(result)
"""

# 示例2: 字符串处理
string_processing_example = """
# 字符串处理示例
text = "Hello, ComfyUI Python Editor!"

# 各种字符串操作
upper_text = text.upper()
lower_text = text.lower()
word_count = len(text.split())
char_count = len(text)

result = {
    "original": text,
    "uppercase": upper_text,
    "lowercase": lower_text,
    "word_count": word_count,
    "char_count": char_count
}

print(f"字符串处理结果: {result}")
"""

# 示例3: 列表和数据处理
data_processing_example = """
# 数据处理示例
import json

# 创建一些示例数据
data = [
    {"name": "Alice", "age": 25, "score": 85},
    {"name": "Bob", "age": 30, "score": 92},
    {"name": "Charlie", "age": 35, "score": 78},
    {"name": "<PERSON>", "age": 28, "score": 96}
]

# 数据分析
total_score = sum(person["score"] for person in data)
average_score = total_score / len(data)
highest_score = max(data, key=lambda x: x["score"])
lowest_score = min(data, key=lambda x: x["score"])

result = {
    "total_people": len(data),
    "average_score": round(average_score, 2),
    "highest_scorer": highest_score,
    "lowest_scorer": lowest_score
}

print(f"数据分析结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
"""

# 示例4: 使用输入数据
input_data_example = """
# 使用输入数据示例
# input_data 变量会自动注入到代码中
import json

try:
    # 尝试解析输入数据为JSON
    data = json.loads(input_data)
    
    if isinstance(data, dict):
        # 如果是字典，处理键值对
        result = f"接收到字典数据，包含 {len(data)} 个键: {list(data.keys())}"
    elif isinstance(data, list):
        # 如果是列表，处理列表项
        result = f"接收到列表数据，包含 {len(data)} 个项目"
    else:
        result = f"接收到数据类型: {type(data).__name__}, 值: {data}"
        
except json.JSONDecodeError:
    # 如果不是JSON，作为普通字符串处理
    result = f"接收到文本数据: '{input_data}' (长度: {len(input_data)})"

print(result)
"""

# 示例5: 图像处理相关（适用于ComfyUI）
image_processing_example = """
# 图像处理相关示例
import math

# 计算图像尺寸相关的参数
width = 1024
height = 768
aspect_ratio = width / height

# 计算不同尺寸的缩放
scales = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
scaled_sizes = []

for scale in scales:
    new_width = int(width * scale)
    new_height = int(height * scale)
    scaled_sizes.append({
        "scale": scale,
        "width": new_width,
        "height": new_height,
        "pixels": new_width * new_height
    })

result = {
    "original": {"width": width, "height": height, "aspect_ratio": round(aspect_ratio, 3)},
    "scaled_sizes": scaled_sizes
}

print(f"图像尺寸计算结果:")
for size in scaled_sizes:
    print(f"  {size['scale']}x: {size['width']}x{size['height']} ({size['pixels']:,} pixels)")
"""

# 示例6: 文件路径处理
file_path_example = """
# 文件路径处理示例
import os

# 示例路径
paths = [
    "/path/to/image.jpg",
    "C:\\Users\\<USER>\\Documents\\file.txt",
    "relative/path/to/data.json",
    "image_001.png"
]

processed_paths = []
for path in paths:
    info = {
        "original": path,
        "basename": os.path.basename(path),
        "dirname": os.path.dirname(path),
        "name": os.path.splitext(os.path.basename(path))[0],
        "extension": os.path.splitext(path)[1],
        "is_absolute": os.path.isabs(path)
    }
    processed_paths.append(info)

result = "文件路径处理结果:"
for info in processed_paths:
    result += f"\\n原路径: {info['original']}"
    result += f"\\n  文件名: {info['basename']}"
    result += f"\\n  目录: {info['dirname']}"
    result += f"\\n  名称: {info['name']}"
    result += f"\\n  扩展名: {info['extension']}"
    result += f"\\n  绝对路径: {info['is_absolute']}"
    result += "\\n"

print(result)
"""

if __name__ == "__main__":
    print("Python代码编辑器节点使用示例")
    print("=" * 50)
    print("这些示例可以直接复制到Python代码编辑器节点中使用：")
    print()
    
    examples = [
        ("基本数学计算", basic_math_example),
        ("字符串处理", string_processing_example),
        ("数据处理", data_processing_example),
        ("使用输入数据", input_data_example),
        ("图像处理相关", image_processing_example),
        ("文件路径处理", file_path_example)
    ]
    
    for i, (title, code) in enumerate(examples, 1):
        print(f"{i}. {title}")
        print("-" * 30)
        print(code.strip())
        print()
