# ComfyUI Common Utility

A comprehensive collection of utility nodes for ComfyUI that provides essential file operations, string manipulation, and list processing capabilities.

## Features

### File Operations
- **Copy File**: Copy files from source to destination with options for directory creation and overwrite protection
- **Move/Rename File**: Move or rename files with safety checks
- **Delete File**: Safely delete files with confirmation requirement
- **List Directory**: List files and directories with filtering and recursive options
- **Create Directory**: Create directories with optional parent directory creation
- **File/Directory Exists**: Check if files or directories exist and get their type
- **Read Text File**: Read content from text files with encoding support
- **Write Text File**: Write content to text files with encoding and safety options
- **Get File Info**: Get detailed information about files including size, dates, and permissions

### String Operations
- **Split String**: Split strings into lists using custom delimiters
- **Join Strings**: Join string lists with custom delimiters
- **Replace Text**: Replace text using simple or regex patterns with case sensitivity options

### List Operations
- **Filter List**: Filter lists based on various criteria (contains, starts with, ends with, regex, length)
- **Sort List**: Sort lists alphabetically, by length, or numerically
- **Random Choice**: Randomly select items from lists with seed support and duplicate options

### Logic Operations
- **Conditional String**: Return different strings based on boolean conditions
- **String Comparison**: Compare strings with various criteria (equals, contains, starts with, etc.)
- **Number Comparison**: Compare numbers with tolerance support
- **Logical Operations**: Perform AND, OR, XOR, NAND, NOR operations on boolean values
- **Counter**: A persistent counter that increments with each execution

### System Operations
- **Environment Variable**: Get, set, or check existence of environment variables

### Data Operations
- **JSON Processor**: Parse, stringify, validate JSON data and extract values by key path

## Installation

1. Clone or download this repository into your ComfyUI custom_nodes directory:
   ```
   cd ComfyUI/custom_nodes
   git clone <repository_url> comfyui_common_utility
   ```

2. Restart ComfyUI

3. The nodes will appear in the "Common Utility" category with subcategories:
   - Common Utility/File Operations
   - Common Utility/String Operations
   - Common Utility/List Operations
   - Common Utility/Logic Operations
   - Common Utility/System Operations
   - Common Utility/Data Operations

## Usage Examples

### File Operations

#### Copy File
- **Source Path**: `/path/to/source/file.txt`
- **Destination Path**: `/path/to/destination/file.txt`
- **Create Dirs**: `True` (creates destination directories if they don't exist)
- **Overwrite**: `False` (prevents overwriting existing files)

#### List Directory
- **Directory Path**: `/path/to/directory`
- **Include Files**: `True`
- **Include Dirs**: `True`
- **Recursive**: `False`
- **File Pattern**: `*.txt` (only list .txt files)

### String Operations

#### Split String
- **Text**: `"apple,banana,cherry"`
- **Delimiter**: `","`
- **Remove Empty**: `True`
- **Strip Whitespace**: `True`
- **Output**: List with 3 items

#### Replace Text
- **Text**: `"Hello World"`
- **Search**: `"World"`
- **Replace**: `"ComfyUI"`
- **Use Regex**: `False`
- **Result**: `"Hello ComfyUI"`

### List Operations

#### Filter List
- **String List**: 
  ```
  apple
  banana
  cherry
  apricot
  ```
- **Filter Type**: `"starts_with"`
- **Filter Value**: `"ap"`
- **Result**: 
  ```
  apple
  apricot
  ```

#### Random Choice
- **String List**: List of items
- **Count**: `2`
- **Allow Duplicates**: `False`
- **Seed**: `42` (for reproducible results)

## Safety Features

- **Path Normalization**: All file paths are normalized and made absolute for safety
- **Overwrite Protection**: File operations include overwrite protection options
- **Directory Creation**: Automatic directory creation when needed
- **Error Handling**: Comprehensive error handling with descriptive messages
- **Confirmation Requirements**: Destructive operations like file deletion require explicit confirmation

## Node Categories

All nodes are organized under the "Common Utility" category:

```
Common Utility/
├── File Operations/
│   ├── Copy File
│   ├── Move/Rename File
│   ├── Delete File
│   ├── List Directory
│   ├── Create Directory
│   ├── File/Directory Exists
│   ├── Read Text File
│   ├── Write Text File
│   └── Get File Info
├── String Operations/
│   ├── Split String
│   ├── Join Strings
│   └── Replace Text
├── List Operations/
│   ├── Filter List
│   ├── Sort List
│   └── Random Choice
├── Logic Operations/
│   ├── Conditional String
│   ├── String Comparison
│   ├── Number Comparison
│   ├── Logical Operations
│   └── Counter
├── System Operations/
│   └── Environment Variable
└── Data Operations/
    └── JSON Processor
```

## Return Values

Most nodes return multiple outputs:
- **Result/Content**: The main result of the operation
- **Success/Count**: Boolean success flag or count of items
- **Message**: Descriptive message about the operation result

## Requirements

- ComfyUI
- Python 3.7+
- Standard Python libraries (os, shutil, re, json, random, pathlib)

## License

This project is provided as-is for use with ComfyUI. Please ensure you have appropriate permissions for any file operations you perform.

## Contributing

Feel free to submit issues and enhancement requests!
