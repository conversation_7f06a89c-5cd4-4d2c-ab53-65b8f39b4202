#!/usr/bin/env python3
"""
测试新的按钮实现
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from logic_operations import PythonCodeEditorAdvanced

def test_advanced_editor_without_button():
    """测试高级编辑器（不使用按钮参数）"""
    print("🧪 测试高级Python代码编辑器（新按钮实现）")
    print("=" * 50)
    
    editor = PythonCodeEditorAdvanced()
    
    # 测试代码
    test_code = """
# 测试新的按钮实现
print("🚀 新的按钮实现测试")

# 处理输入数据
if input_data:
    print(f"输入数据: {input_data}")

# 处理输入数组
if input_array:
    print(f"输入数组: {input_array}")
    print(f"数组类型分布:")
    
    type_counts = {}
    for item in input_array:
        item_type = type(item).__name__
        type_counts[item_type] = type_counts.get(item_type, 0) + 1
    
    for type_name, count in type_counts.items():
        print(f"  {type_name}: {count} 个")

# 数学计算
if input_number > 0:
    import math
    print(f"\\n数学计算:")
    print(f"  输入数字: {input_number}")
    print(f"  平方: {input_number ** 2}")
    print(f"  平方根: {math.sqrt(input_number):.3f}")
    print(f"  正弦值: {math.sin(input_number):.3f}")

# 生成结果
result = {
    "message": "新按钮实现测试成功!",
    "data_processed": {
        "input_data_length": len(input_data) if input_data else 0,
        "array_items": len(input_array) if input_array else 0,
        "number_value": input_number
    },
    "button_implementation": "improved",
    "ui_enhancement": "external_editor_button"
}

print(f"\\n✅ 测试完成!")
print(f"结果: {result}")
"""
    
    # 执行测试（不使用按钮参数）
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=test_code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        unique_id="test_node_123",
        input_data="测试数据 - 新按钮实现",
        input_array=[1, 2, "测试", 3.14, True, None, "完成"],
        input_number=25.0,
        input_image=None
    )
    
    print(f"📤 输出:\n{output}")
    print(f"🎯 结果值: {result_value}")
    print(f"📦 结果对象类型: {type(result_object)}")
    print(f"✅ 成功: {success}")
    print(f"ℹ️ 执行信息: {execution_info}")
    print()

def test_input_types_validation():
    """测试输入类型验证"""
    print("🔍 测试输入类型验证")
    print("=" * 50)
    
    editor = PythonCodeEditorAdvanced()
    
    # 检查INPUT_TYPES方法
    input_types = editor.INPUT_TYPES()
    
    print("📋 节点输入类型配置:")
    print(f"  必需参数: {list(input_types['required'].keys())}")
    print(f"  可选参数: {list(input_types.get('optional', {}).keys())}")
    print(f"  隐藏参数: {list(input_types.get('hidden', {}).keys())}")
    
    # 验证没有open_editor参数
    if 'open_editor' not in input_types['required']:
        print("✅ 成功: open_editor 参数已从必需参数中移除")
    else:
        print("❌ 错误: open_editor 参数仍在必需参数中")
    
    # 验证有unique_id隐藏参数
    if 'unique_id' in input_types.get('hidden', {}):
        print("✅ 成功: unique_id 隐藏参数已添加")
    else:
        print("❌ 错误: unique_id 隐藏参数未找到")
    
    print()

def test_ui_features_info():
    """显示UI功能信息"""
    print("🎨 UI功能信息")
    print("=" * 50)
    
    print("📁 更新的文件:")
    print("  ✓ logic_operations.py - 移除了open_editor参数，添加了unique_id")
    print("  ✓ web/extensions/python_code_editor.js - 实现了自定义按钮widget")
    print("  ✓ web/extensions/python_code_editor.css - 按钮样式")
    
    print("\n🔧 新的按钮实现:")
    print("  ✓ 真正的按钮widget，不是toggle")
    print("  ✓ 点击即可打开外部编辑器")
    print("  ✓ 不需要运行工作流就能使用")
    print("  ✓ 自定义绘制和鼠标事件处理")
    print("  ✓ 集成到节点widget系统中")
    
    print("\n🖱️ 按钮功能:")
    print("  ✓ 文本: '🚀 Open External Editor'")
    print("  ✓ 颜色: 蓝色背景 (#0e639c)")
    print("  ✓ 悬停效果: 更亮的蓝色 (#1177bb)")
    print("  ✓ 点击响应: 立即打开外部编辑器窗口")
    print("  ✓ 代码同步: 编辑器关闭时自动更新节点代码")
    
    print("\n📋 使用说明:")
    print("  1. 重启ComfyUI以加载新的JavaScript代码")
    print("  2. 添加 'Python Code Editor (Advanced)' 节点")
    print("  3. 在节点底部会看到 '🚀 Open External Editor' 按钮")
    print("  4. 点击按钮即可打开外部编辑器（无需运行工作流）")
    print("  5. 在外部编辑器中编辑代码，保存并关闭")
    print("  6. 代码会自动同步回ComfyUI节点")
    
    print()

def test_node_registration():
    """测试节点注册"""
    print("📝 测试节点注册")
    print("=" * 50)
    
    try:
        from logic_operations import LOGIC_NODE_CLASS_MAPPINGS, LOGIC_NODE_DISPLAY_NAME_MAPPINGS
        
        # 检查高级编辑器节点
        if "PythonCodeEditorAdvanced" in LOGIC_NODE_CLASS_MAPPINGS:
            print("✅ PythonCodeEditorAdvanced 节点已注册")
            
            # 测试节点实例化
            NodeClass = LOGIC_NODE_CLASS_MAPPINGS["PythonCodeEditorAdvanced"]
            node_instance = NodeClass()
            print("✅ 节点实例化成功")
            
            # 测试INPUT_TYPES
            input_types = NodeClass.INPUT_TYPES()
            print("✅ INPUT_TYPES 方法正常")
            
            # 检查返回类型
            print(f"✅ RETURN_TYPES: {NodeClass.RETURN_TYPES}")
            print(f"✅ RETURN_NAMES: {NodeClass.RETURN_NAMES}")
            print(f"✅ FUNCTION: {NodeClass.FUNCTION}")
            print(f"✅ CATEGORY: {NodeClass.CATEGORY}")
            
        else:
            print("❌ PythonCodeEditorAdvanced 节点未注册")
            
    except Exception as e:
        print(f"❌ 节点注册测试失败: {e}")
    
    print()

if __name__ == "__main__":
    print("🎉 新按钮实现测试")
    print("=" * 60)
    print()
    
    test_node_registration()
    test_input_types_validation()
    test_advanced_editor_without_button()
    test_ui_features_info()
    
    print("🎊 测试完成!")
    print("\n📋 总结:")
    print("✅ 移除了toggle类型的open_editor参数")
    print("🔧 实现了真正的按钮widget")
    print("🖱️ 按钮可以在不运行工作流的情况下使用")
    print("🎨 改进了UI交互体验")
    print("📦 保持了所有原有功能")
    print("\n🚀 准备在ComfyUI中使用! 记得重启ComfyUI以加载新的按钮实现。")
