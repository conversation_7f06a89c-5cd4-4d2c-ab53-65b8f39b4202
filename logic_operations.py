import os
import json
import sys
import subprocess
import tempfile
import traceback
from typing import Any, Union


class ConditionalString:
    """Return one of two strings based on a boolean condition"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "condition": ("BOOLEAN", {"default": True}),
                "if_true": ("STRING", {"default": "", "multiline": True}),
                "if_false": ("STRING", {"default": "", "multiline": True})
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("result", "result_message")
    FUNCTION = "conditional_string"
    CATEGORY = "Common Utility/Logic Operations"
    
    def conditional_string(self, condition: bool, if_true: str, if_false: str):
        result = if_true if condition else if_false
        message = f"Condition was {'True' if condition else 'False'}, returned: '{result[:50]}...'" if len(result) > 50 else f"Condition was {'True' if condition else 'False'}, returned: '{result}'"
        return (result, message)


class StringComparison:
    """Compare two strings and return boolean result"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "string1": ("STRING", {"default": "", "multiline": False}),
                "string2": ("STRING", {"default": "", "multiline": False}),
                "comparison": (["equals", "not_equals", "contains", "starts_with", "ends_with", "length_greater", "length_less", "length_equal"], {"default": "equals"}),
                "case_sensitive": ("BOOLEAN", {"default": True})
            }
        }
    
    RETURN_TYPES = ("BOOLEAN", "STRING")
    RETURN_NAMES = ("result", "result_message")
    FUNCTION = "compare_strings"
    CATEGORY = "Common Utility/Logic Operations"
    
    def compare_strings(self, string1: str, string2: str, comparison: str, case_sensitive: bool):
        try:
            if not case_sensitive:
                s1 = string1.lower()
                s2 = string2.lower()
            else:
                s1 = string1
                s2 = string2
            
            if comparison == "equals":
                result = s1 == s2
            elif comparison == "not_equals":
                result = s1 != s2
            elif comparison == "contains":
                result = s2 in s1
            elif comparison == "starts_with":
                result = s1.startswith(s2)
            elif comparison == "ends_with":
                result = s1.endswith(s2)
            elif comparison == "length_greater":
                result = len(string1) > len(string2)
            elif comparison == "length_less":
                result = len(string1) < len(string2)
            elif comparison == "length_equal":
                result = len(string1) == len(string2)
            else:
                result = False
            
            message = f"'{string1}' {comparison} '{string2}' = {result}"
            return (result, message)
            
        except Exception as e:
            return (False, f"Error comparing strings: {str(e)}")


class NumberComparison:
    """Compare two numbers and return boolean result"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "number1": ("FLOAT", {"default": 0.0}),
                "number2": ("FLOAT", {"default": 0.0}),
                "comparison": (["equals", "not_equals", "greater", "greater_equal", "less", "less_equal"], {"default": "equals"}),
                "tolerance": ("FLOAT", {"default": 0.0001, "min": 0.0, "step": 0.0001})
            }
        }
    
    RETURN_TYPES = ("BOOLEAN", "STRING")
    RETURN_NAMES = ("result", "result_message")
    FUNCTION = "compare_numbers"
    CATEGORY = "Common Utility/Logic Operations"
    
    def compare_numbers(self, number1: float, number2: float, comparison: str, tolerance: float):
        try:
            if comparison == "equals":
                result = abs(number1 - number2) <= tolerance
            elif comparison == "not_equals":
                result = abs(number1 - number2) > tolerance
            elif comparison == "greater":
                result = number1 > number2
            elif comparison == "greater_equal":
                result = number1 >= number2
            elif comparison == "less":
                result = number1 < number2
            elif comparison == "less_equal":
                result = number1 <= number2
            else:
                result = False
            
            message = f"{number1} {comparison} {number2} = {result}"
            return (result, message)
            
        except Exception as e:
            return (False, f"Error comparing numbers: {str(e)}")


class LogicalOperations:
    """Perform logical operations on boolean values"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "bool1": ("BOOLEAN", {"default": True}),
                "bool2": ("BOOLEAN", {"default": True}),
                "operation": (["AND", "OR", "XOR", "NAND", "NOR"], {"default": "AND"})
            }
        }
    
    RETURN_TYPES = ("BOOLEAN", "STRING")
    RETURN_NAMES = ("result", "result_message")
    FUNCTION = "logical_operation"
    CATEGORY = "Common Utility/Logic Operations"
    
    def logical_operation(self, bool1: bool, bool2: bool, operation: str):
        try:
            if operation == "AND":
                result = bool1 and bool2
            elif operation == "OR":
                result = bool1 or bool2
            elif operation == "XOR":
                result = bool1 ^ bool2
            elif operation == "NAND":
                result = not (bool1 and bool2)
            elif operation == "NOR":
                result = not (bool1 or bool2)
            else:
                result = False
            
            message = f"{bool1} {operation} {bool2} = {result}"
            return (result, message)
            
        except Exception as e:
            return (False, f"Error in logical operation: {str(e)}")


class CounterNode:
    """A simple counter that increments each time it's executed"""
    
    def __init__(self):
        self.counter = 0
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "reset": ("BOOLEAN", {"default": False}),
                "increment": ("INT", {"default": 1, "min": -1000, "max": 1000}),
                "start_value": ("INT", {"default": 0, "min": -1000000, "max": 1000000})
            }
        }
    
    RETURN_TYPES = ("INT", "STRING")
    RETURN_NAMES = ("count", "result_message")
    FUNCTION = "count"
    CATEGORY = "Common Utility/Logic Operations"
    
    def count(self, reset: bool, increment: int, start_value: int):
        if reset:
            self.counter = start_value
            message = f"Counter reset to {start_value}"
        else:
            self.counter += increment
            message = f"Counter incremented by {increment}, now at {self.counter}"
        
        return (self.counter, message)


class EnvironmentVariable:
    """Get or set environment variables"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "variable_name": ("STRING", {"default": "", "multiline": False}),
                "operation": (["get", "set", "check_exists"], {"default": "get"}),
                "value": ("STRING", {"default": "", "multiline": False}),
                "default_value": ("STRING", {"default": "", "multiline": False})
            }
        }
    
    RETURN_TYPES = ("STRING", "BOOLEAN", "STRING")
    RETURN_NAMES = ("value", "exists", "result_message")
    FUNCTION = "env_operation"
    CATEGORY = "Common Utility/System Operations"
    
    def env_operation(self, variable_name: str, operation: str, value: str, default_value: str):
        try:
            if not variable_name:
                return ("", False, "Variable name cannot be empty")
            
            if operation == "get":
                env_value = os.environ.get(variable_name, default_value)
                exists = variable_name in os.environ
                message = f"Environment variable '{variable_name}' = '{env_value}'"
                return (env_value, exists, message)
                
            elif operation == "set":
                os.environ[variable_name] = value
                message = f"Set environment variable '{variable_name}' = '{value}'"
                return (value, True, message)
                
            elif operation == "check_exists":
                exists = variable_name in os.environ
                env_value = os.environ.get(variable_name, "")
                message = f"Environment variable '{variable_name}' {'exists' if exists else 'does not exist'}"
                return (env_value, exists, message)
            
            else:
                return ("", False, f"Unknown operation: {operation}")
                
        except Exception as e:
            return ("", False, f"Error with environment variable: {str(e)}")


class JSONProcessor:
    """Process JSON data - parse, stringify, extract values"""

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "json_input": ("STRING", {"default": "{}", "multiline": True}),
                "operation": (["parse", "stringify", "extract_key", "validate"], {"default": "parse"}),
                "key_path": ("STRING", {"default": "", "multiline": False}),
                "pretty_print": ("BOOLEAN", {"default": True})
            }
        }

    RETURN_TYPES = ("STRING", "BOOLEAN", "STRING")
    RETURN_NAMES = ("result", "success", "result_message")
    FUNCTION = "process_json"
    CATEGORY = "Common Utility/Data Operations"

    def process_json(self, json_input: str, operation: str, key_path: str, pretty_print: bool):
        try:
            if operation == "parse":
                data = json.loads(json_input)
                if pretty_print:
                    result = json.dumps(data, indent=2, ensure_ascii=False)
                else:
                    result = json.dumps(data, ensure_ascii=False)
                return (result, True, "JSON parsed successfully")

            elif operation == "stringify":
                # Assume input is already a JSON object as string
                data = json.loads(json_input)
                result = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
                return (result, True, "JSON stringified successfully")

            elif operation == "extract_key":
                if not key_path:
                    return ("", False, "Key path cannot be empty for extract operation")

                data = json.loads(json_input)
                keys = key_path.split('.')
                current = data

                for key in keys:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    elif isinstance(current, list) and key.isdigit():
                        index = int(key)
                        if 0 <= index < len(current):
                            current = current[index]
                        else:
                            return ("", False, f"Index {index} out of range")
                    else:
                        return ("", False, f"Key '{key}' not found")

                result = json.dumps(current, ensure_ascii=False) if isinstance(current, (dict, list)) else str(current)
                return (result, True, f"Extracted value for key path '{key_path}'")

            elif operation == "validate":
                json.loads(json_input)
                return (json_input, True, "JSON is valid")

            else:
                return ("", False, f"Unknown operation: {operation}")

        except json.JSONDecodeError as e:
            return ("", False, f"JSON decode error: {str(e)}")
        except Exception as e:
            return ("", False, f"Error processing JSON: {str(e)}")


class PythonCodeEditor:
    """A Python code editor and executor node for ComfyUI"""

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "python_code": ("STRING", {
                    "default": "# Python Code Editor\n# Write your Python code here\n\n# Available variables:\n# - input_data: string input\n# - input_array: array input\n# - input_number: number input\n\n# Example:\nresult = 'Hello from Python!'\nprint(result)\n\n# The last expression or 'result' variable will be returned",
                    "multiline": True,
                    "dynamicPrompts": False,
                    "tooltip": "Python code to execute. Use Ctrl+Enter to add new lines."
                }),
                "execution_mode": (["execute", "syntax_check"], {"default": "execute"}),
                "timeout_seconds": ("INT", {"default": 30, "min": 1, "max": 300}),
                "capture_output": ("BOOLEAN", {"default": True}),
                "return_variable": ("STRING", {"default": "result", "multiline": False})
            },
            "optional": {
                "input_data": ("STRING", {"default": "", "multiline": True}),
                "input_array": ("*", {"tooltip": "Array input that will be available as 'input_array' variable in your code"}),
                "input_number": ("FLOAT", {"default": 0.0, "tooltip": "Number input that will be available as 'input_number' variable in your code"})
            }
        }

    RETURN_TYPES = ("STRING", "STRING", "BOOLEAN", "STRING")
    RETURN_NAMES = ("output", "result_value", "success", "execution_info")
    FUNCTION = "execute_python_code"
    CATEGORY = "Common Utility/Code Operations"

    def execute_python_code(self, python_code: str, execution_mode: str, timeout_seconds: int,
                          capture_output: bool, return_variable: str, input_data: str = "",
                          input_array=None, input_number: float = 0.0):
        """Execute Python code and return results"""

        if execution_mode == "syntax_check":
            return self._syntax_check(python_code)

        try:
            # Create a temporary file for the Python code
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
                # Prepare the code with input data injection if provided
                full_code = self._prepare_code(python_code, input_data, input_array, input_number, return_variable)
                temp_file.write(full_code)
                temp_file_path = temp_file.name

            try:
                # Execute the code using the specified Python interpreter
                python_exe = r"K:\AI_V2\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe"

                # Check if the specified Python executable exists
                if not os.path.exists(python_exe):
                    # Fallback to system Python if the specified one doesn't exist
                    python_exe = sys.executable

                # Execute the code
                result = subprocess.run(
                    [python_exe, temp_file_path],
                    capture_output=capture_output,
                    text=True,
                    timeout=timeout_seconds,
                    cwd=os.path.dirname(temp_file_path)
                )

                # Process results
                output = result.stdout if result.stdout else ""
                error_output = result.stderr if result.stderr else ""

                if result.returncode == 0:
                    # Try to extract the return value from the last line of output
                    result_value = self._extract_result_value(output, return_variable)

                    execution_info = f"Execution successful (exit code: {result.returncode})"
                    if error_output:
                        execution_info += f"\nWarnings: {error_output}"

                    return (output, result_value, True, execution_info)
                else:
                    execution_info = f"Execution failed (exit code: {result.returncode})\nError: {error_output}"
                    return (output, "", False, execution_info)

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except subprocess.TimeoutExpired:
            return ("", "", False, f"Code execution timed out after {timeout_seconds} seconds")
        except Exception as e:
            return ("", "", False, f"Error executing code: {str(e)}\n{traceback.format_exc()}")

    def _syntax_check(self, python_code: str):
        """Check Python code syntax without executing"""
        try:
            compile(python_code, '<string>', 'exec')
            return ("", "", True, "Syntax check passed - no syntax errors found")
        except SyntaxError as e:
            error_msg = f"Syntax Error at line {e.lineno}: {e.msg}"
            if e.text:
                error_msg += f"\nCode: {e.text.strip()}"
            return ("", "", False, error_msg)
        except Exception as e:
            return ("", "", False, f"Error during syntax check: {str(e)}")

    def _prepare_code(self, python_code: str, input_data: str, input_array, input_number: float, return_variable: str):
        """Prepare the code for execution with input data and result extraction"""
        prepared_code = ""

        # Add input data as variables if provided
        if input_data.strip():
            prepared_code += f"# Input data provided by ComfyUI\ninput_data = '''{input_data}'''\n\n"
        else:
            prepared_code += "# Input data (empty)\ninput_data = ''\n\n"

        # Add input array
        if input_array is not None:
            # Convert input_array to a proper Python representation
            if isinstance(input_array, (list, tuple)):
                prepared_code += f"# Input array provided by ComfyUI\ninput_array = {repr(input_array)}\n\n"
            else:
                # If it's a single value, wrap it in a list
                prepared_code += f"# Input array (single value) provided by ComfyUI\ninput_array = [{repr(input_array)}]\n\n"
        else:
            prepared_code += "# Input array (empty)\ninput_array = []\n\n"

        # Add input number
        prepared_code += f"# Input number provided by ComfyUI\ninput_number = {input_number}\n\n"

        # Add the user's code
        prepared_code += python_code + "\n\n"

        # Add result extraction logic
        prepared_code += f"""
# Result extraction for ComfyUI
import sys
try:
    if '{return_variable}' in locals():
        print(f"__COMFYUI_RESULT__:{{{return_variable}}}")
    elif '{return_variable}' in globals():
        print(f"__COMFYUI_RESULT__:{{{return_variable}}}")
except:
    pass
"""

        return prepared_code

    def _extract_result_value(self, output: str, return_variable: str):
        """Extract the result value from the output"""
        lines = output.split('\n')
        for line in lines:
            if line.startswith("__COMFYUI_RESULT__:"):
                return line[19:]  # Remove the prefix

        # If no specific result found, return the last non-empty line
        for line in reversed(lines):
            if line.strip():
                return line.strip()

        return ""


class PythonCodeEditorAdvanced:
    """An advanced Python code editor with enhanced UI and array support"""

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "python_code": ("STRING", {
                    "default": "# Advanced Python Code Editor\n# Enhanced UI with syntax highlighting\n\n# Available variables:\n# - input_data: string input\n# - input_array: array/list input\n# - input_number: number input\n# - input_image: image tensor (if connected)\n\n# Example with array processing:\nif input_array:\n    result = f'Array has {len(input_array)} items: {input_array}'\nelse:\n    result = 'Hello from Advanced Python Editor!'\n\nprint(result)",
                    "multiline": True,
                    "dynamicPrompts": False,
                    "tooltip": "Python code with enhanced editor. Click 'Open Editor' button for better editing experience."
                }),
                "execution_mode": (["execute", "syntax_check"], {"default": "execute"}),
                "timeout_seconds": ("INT", {"default": 30, "min": 1, "max": 300}),
                "capture_output": ("BOOLEAN", {"default": True}),
                "return_variable": ("STRING", {"default": "result", "multiline": False}),
                "open_editor": ("BOOLEAN", {"default": False, "tooltip": "Click to open external code editor window"})
            },
            "optional": {
                "input_data": ("STRING", {"default": "", "multiline": True}),
                "input_array": ("*", {"tooltip": "Array/List input - can accept any type that will be converted to a list"}),
                "input_number": ("FLOAT", {"default": 0.0}),
                "input_image": ("IMAGE", {"tooltip": "Image tensor input (optional)"})
            }
        }

    RETURN_TYPES = ("STRING", "STRING", "BOOLEAN", "STRING", "*")
    RETURN_NAMES = ("output", "result_value", "success", "execution_info", "result_object")
    FUNCTION = "execute_python_code_advanced"
    CATEGORY = "Common Utility/Code Operations"

    def execute_python_code_advanced(self, python_code: str, execution_mode: str, timeout_seconds: int,
                                   capture_output: bool, return_variable: str, open_editor: bool = False,
                                   input_data: str = "", input_array=None, input_number: float = 0.0,
                                   input_image=None):
        """Execute Python code with advanced features and return results"""

        if execution_mode == "syntax_check":
            return self._syntax_check_advanced(python_code)

        try:
            # Create a temporary file for the Python code
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
                # Prepare the code with all input data injection
                full_code = self._prepare_code_advanced(python_code, input_data, input_array,
                                                      input_number, input_image, return_variable)
                temp_file.write(full_code)
                temp_file_path = temp_file.name

            try:
                # Execute the code using the specified Python interpreter
                python_exe = r"K:\AI_V2\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe"

                # Check if the specified Python executable exists
                if not os.path.exists(python_exe):
                    # Fallback to system Python if the specified one doesn't exist
                    python_exe = sys.executable

                # Execute the code
                result = subprocess.run(
                    [python_exe, temp_file_path],
                    capture_output=capture_output,
                    text=True,
                    timeout=timeout_seconds,
                    cwd=os.path.dirname(temp_file_path)
                )

                # Process results
                output = result.stdout if result.stdout else ""
                error_output = result.stderr if result.stderr else ""

                if result.returncode == 0:
                    # Try to extract the return value from the last line of output
                    result_value = self._extract_result_value(output, return_variable)

                    # Try to parse the result as a Python object
                    result_object = self._parse_result_object(result_value)

                    execution_info = f"Execution successful (exit code: {result.returncode})"
                    if error_output:
                        execution_info += f"\nWarnings: {error_output}"

                    return (output, result_value, True, execution_info, result_object)
                else:
                    execution_info = f"Execution failed (exit code: {result.returncode})\nError: {error_output}"
                    return (output, "", False, execution_info, None)

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except subprocess.TimeoutExpired:
            return ("", "", False, f"Code execution timed out after {timeout_seconds} seconds", None)
        except Exception as e:
            return ("", "", False, f"Error executing code: {str(e)}\n{traceback.format_exc()}", None)

    def _syntax_check_advanced(self, python_code: str):
        """Advanced syntax check with more detailed error reporting"""
        try:
            compile(python_code, '<string>', 'exec')
            return ("", "", True, "✓ Syntax check passed - no syntax errors found", None)
        except SyntaxError as e:
            error_msg = f"❌ Syntax Error at line {e.lineno}: {e.msg}"
            if e.text:
                error_msg += f"\nCode: {e.text.strip()}"
                if e.offset:
                    error_msg += f"\n{' ' * (e.offset - 1)}^"
            return ("", "", False, error_msg, None)
        except Exception as e:
            return ("", "", False, f"❌ Error during syntax check: {str(e)}", None)

    def _prepare_code_advanced(self, python_code: str, input_data: str, input_array,
                             input_number: float, input_image, return_variable: str):
        """Prepare the code for execution with all input data and advanced features"""
        prepared_code = ""

        # Add imports that might be useful
        prepared_code += "# Auto-imported modules\nimport sys, os, json, math\nimport numpy as np\n\n"

        # Add input data as variables
        if input_data.strip():
            prepared_code += f"# Input data provided by ComfyUI\ninput_data = '''{input_data}'''\n\n"
        else:
            prepared_code += "# Input data (empty)\ninput_data = ''\n\n"

        # Add input array with better handling
        if input_array is not None:
            if isinstance(input_array, (list, tuple)):
                prepared_code += f"# Input array provided by ComfyUI\ninput_array = {repr(list(input_array))}\n\n"
            else:
                # Convert single value to list
                prepared_code += f"# Input array (single value) provided by ComfyUI\ninput_array = [{repr(input_array)}]\n\n"
        else:
            prepared_code += "# Input array (empty)\ninput_array = []\n\n"

        # Add input number
        prepared_code += f"# Input number provided by ComfyUI\ninput_number = {input_number}\n\n"

        # Add input image if provided
        if input_image is not None:
            prepared_code += "# Input image provided by ComfyUI\n"
            prepared_code += f"# Image shape: {input_image.shape if hasattr(input_image, 'shape') else 'Unknown'}\n"
            prepared_code += "input_image = 'IMAGE_TENSOR_PROVIDED'  # Actual tensor available in full implementation\n\n"
        else:
            prepared_code += "# Input image (not provided)\ninput_image = None\n\n"

        # Add the user's code
        prepared_code += "# User code starts here\n"
        prepared_code += python_code + "\n\n"

        # Add result extraction logic
        prepared_code += f"""
# Result extraction for ComfyUI
try:
    if '{return_variable}' in locals():
        print(f"__COMFYUI_RESULT__:{{{return_variable}}}")
    elif '{return_variable}' in globals():
        print(f"__COMFYUI_RESULT__:{{{return_variable}}}")
    else:
        print("__COMFYUI_RESULT__:None")
except Exception as e:
    print(f"__COMFYUI_RESULT_ERROR__:{{str(e)}}")
"""

        return prepared_code

    def _parse_result_object(self, result_value: str):
        """Try to parse the result value as a Python object"""
        if not result_value or result_value == "None":
            return None

        try:
            # Try to evaluate as Python literal
            import ast
            return ast.literal_eval(result_value)
        except:
            # If that fails, return as string
            return result_value

    def _extract_result_value(self, output: str, return_variable: str):
        """Extract the result value from the output"""
        lines = output.split('\n')
        for line in lines:
            if line.startswith("__COMFYUI_RESULT__:"):
                return line[19:]  # Remove the prefix
            elif line.startswith("__COMFYUI_RESULT_ERROR__:"):
                return f"ERROR: {line[26:]}"

        # If no specific result found, return the last non-empty line
        for line in reversed(lines):
            if line.strip():
                return line.strip()

        return ""


# Node mappings for logic operations
LOGIC_NODE_CLASS_MAPPINGS = {
    "ConditionalString": ConditionalString,
    "StringComparison": StringComparison,
    "NumberComparison": NumberComparison,
    "LogicalOperations": LogicalOperations,
    "CounterNode": CounterNode,
    "EnvironmentVariable": EnvironmentVariable,
    "JSONProcessor": JSONProcessor,
    "PythonCodeEditor": PythonCodeEditor,
    "PythonCodeEditorAdvanced": PythonCodeEditorAdvanced,
}

LOGIC_NODE_DISPLAY_NAME_MAPPINGS = {
    "ConditionalString": "Conditional String",
    "StringComparison": "String Comparison",
    "NumberComparison": "Number Comparison",
    "LogicalOperations": "Logical Operations",
    "CounterNode": "Counter",
    "EnvironmentVariable": "Environment Variable",
    "JSONProcessor": "JSON Processor",
    "PythonCodeEditor": "Python Code Editor",
    "PythonCodeEditorAdvanced": "Python Code Editor (Advanced)",
}
