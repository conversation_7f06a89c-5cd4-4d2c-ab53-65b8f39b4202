#!/usr/bin/env python3
"""
Test script for the Advanced Python Code Editor node
"""

import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from logic_operations import PythonCodeEditorAdvanced

def test_basic_functionality():
    """Test basic functionality of the advanced editor"""
    print("Testing Advanced Python Code Editor - Basic Functionality...")
    
    editor = PythonCodeEditorAdvanced()
    
    # Test simple code with all input types
    code = """
# Test all input types
print(f"Input data: {input_data}")
print(f"Input array: {input_array}")
print(f"Input number: {input_number}")
print(f"Input image: {input_image}")

# Process the inputs
if input_array:
    array_sum = sum([x for x in input_array if isinstance(x, (int, float))])
    result = f"Array sum: {array_sum}, Number: {input_number}, Data length: {len(input_data)}"
else:
    result = f"No array provided. Number: {input_number}, Data: '{input_data}'"

print(f"Final result: {result}")
"""
    
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        open_editor=False,
        input_data="Hello from test!",
        input_array=[1, 2, 3, 4, 5],
        input_number=42.5,
        input_image=None
    )
    
    print(f"Output: {output}")
    print(f"Result Value: {result_value}")
    print(f"Result Object: {result_object}")
    print(f"Success: {success}")
    print(f"Execution Info: {execution_info}")
    print("-" * 50)

def test_array_processing():
    """Test array processing capabilities"""
    print("Testing array processing...")
    
    editor = PythonCodeEditorAdvanced()
    
    code = """
# Advanced array processing
import json

print(f"Input array type: {type(input_array)}")
print(f"Input array content: {input_array}")

if input_array:
    # Statistical analysis
    numeric_items = [x for x in input_array if isinstance(x, (int, float))]
    string_items = [x for x in input_array if isinstance(x, str)]
    
    stats = {
        "total_items": len(input_array),
        "numeric_items": len(numeric_items),
        "string_items": len(string_items),
        "numeric_sum": sum(numeric_items) if numeric_items else 0,
        "numeric_avg": sum(numeric_items) / len(numeric_items) if numeric_items else 0,
        "string_lengths": [len(s) for s in string_items]
    }
    
    result = json.dumps(stats, indent=2)
else:
    result = "No array provided for processing"

print(f"Analysis result: {result}")
"""
    
    # Test with mixed array
    test_array = [1, 2, 3, "hello", "world", 4.5, "test"]
    
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        open_editor=False,
        input_data="",
        input_array=test_array,
        input_number=0,
        input_image=None
    )
    
    print(f"Output: {output}")
    print(f"Result Value: {result_value}")
    print(f"Result Object: {result_object}")
    print(f"Success: {success}")
    print(f"Execution Info: {execution_info}")
    print("-" * 50)

def test_syntax_checking():
    """Test advanced syntax checking"""
    print("Testing advanced syntax checking...")
    
    editor = PythonCodeEditorAdvanced()
    
    # Test valid code
    valid_code = """
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

result = [calculate_fibonacci(i) for i in range(10)]
"""
    
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=valid_code,
        execution_mode="syntax_check",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        open_editor=False
    )
    
    print(f"Valid code syntax check - Success: {success}")
    print(f"Info: {execution_info}")
    
    # Test invalid code
    invalid_code = """
def broken_function(
    print("This is missing a closing parenthesis"
    return "broken"
"""
    
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=invalid_code,
        execution_mode="syntax_check",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        open_editor=False
    )
    
    print(f"Invalid code syntax check - Success: {success}")
    print(f"Info: {execution_info}")
    print("-" * 50)

def test_complex_computation():
    """Test complex computation with multiple inputs"""
    print("Testing complex computation...")
    
    editor = PythonCodeEditorAdvanced()
    
    code = """
# Complex data processing example
import json
import math

# Parse input data as JSON if possible
try:
    data_dict = json.loads(input_data) if input_data else {}
except:
    data_dict = {"raw_data": input_data}

# Process array with mathematical operations
processed_array = []
if input_array:
    for item in input_array:
        if isinstance(item, (int, float)):
            # Apply mathematical transformations
            processed_item = {
                "original": item,
                "squared": item ** 2,
                "sqrt": math.sqrt(abs(item)),
                "sin": math.sin(item),
                "multiplied_by_input": item * input_number
            }
            processed_array.append(processed_item)
        else:
            processed_array.append({"original": item, "type": type(item).__name__})

# Create comprehensive result
result = {
    "input_summary": {
        "data_keys": list(data_dict.keys()),
        "array_length": len(input_array) if input_array else 0,
        "input_number": input_number
    },
    "processed_array": processed_array,
    "statistics": {
        "total_numeric_items": len([x for x in (input_array or []) if isinstance(x, (int, float))]),
        "array_sum": sum([x for x in (input_array or []) if isinstance(x, (int, float))]),
        "number_operations": {
            "doubled": input_number * 2,
            "squared": input_number ** 2,
            "factorial": math.factorial(int(abs(input_number))) if abs(input_number) <= 20 else "too_large"
        }
    }
}

print(f"Complex computation completed!")
print(f"Processed {len(processed_array)} array items")
print(f"Input number operations: {result['statistics']['number_operations']}")
"""
    
    # Test with complex inputs
    test_data = '{"name": "test_session", "version": 1.0, "settings": {"debug": true}}'
    test_array = [1, 2, 3, 4, 5, "text", 6.7, 8, 9, 10]
    test_number = 5.0
    
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        open_editor=False,
        input_data=test_data,
        input_array=test_array,
        input_number=test_number,
        input_image=None
    )
    
    print(f"Output: {output}")
    print(f"Result Value: {result_value}")
    print(f"Result Object Type: {type(result_object)}")
    print(f"Success: {success}")
    print(f"Execution Info: {execution_info}")
    print("-" * 50)

def test_error_handling():
    """Test error handling in advanced editor"""
    print("Testing error handling...")
    
    editor = PythonCodeEditorAdvanced()
    
    # Test code that raises an exception
    error_code = """
# This will cause an error
result = input_array[100]  # Index out of range
"""
    
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=error_code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        open_editor=False,
        input_data="",
        input_array=[1, 2, 3],
        input_number=0,
        input_image=None
    )
    
    print(f"Error test - Output: {output}")
    print(f"Error test - Result Value: {result_value}")
    print(f"Error test - Success: {success}")
    print(f"Error test - Execution Info: {execution_info}")
    print("-" * 50)

if __name__ == "__main__":
    print("Advanced Python Code Editor Node Tests")
    print("=" * 60)
    
    test_basic_functionality()
    test_array_processing()
    test_syntax_checking()
    test_complex_computation()
    test_error_handling()
    
    print("All advanced tests completed!")
    print("\n🎉 新功能总结:")
    print("✓ 支持数组输入 (input_array)")
    print("✓ 支持数字输入 (input_number)")
    print("✓ 支持图像输入 (input_image)")
    print("✓ 增强的语法检查")
    print("✓ 更好的错误处理")
    print("✓ 自动导入常用模块")
    print("✓ 结果对象解析")
    print("✓ 前端UI增强 (需要重启ComfyUI生效)")
    print("\n节点已准备好在ComfyUI中使用!")
