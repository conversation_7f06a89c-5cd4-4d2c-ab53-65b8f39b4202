from .file_operations import NO<PERSON>_CLASS_MAPPINGS as FILE_NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS as FILE_NODE_DISPLAY_NAME_MAPPINGS
from .utility_operations import UTILITY_NODE_CLASS_MAPPINGS, UTILITY_NODE_DISPLAY_NAME_MAPPINGS
from .logic_operations import LOGIC_NODE_CLASS_MAPPINGS, LOGIC_NODE_DISPLAY_NAME_MAPPINGS

# Combine all node mappings
NODE_CLASS_MAPPINGS = {
    **FILE_NODE_CLASS_MAPPINGS,
    **UTILITY_NODE_CLASS_MAPPINGS,
    **LOGIC_NODE_CLASS_MAPPINGS
}

NODE_DISPLAY_NAME_MAPPINGS = {
    **FILE_NODE_DISPLAY_NAME_MAPPINGS,
    **UTILITY_NODE_DISPLAY_NAME_MAPPINGS,
    **LOGIC_NODE_DISPLAY_NAME_MAPPINGS
}

__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS']

print("ComfyUI Common Utility nodes loaded:")
print("  - File operations: Copy, Move, Delete, List Directory, Create Directory, File Exists, Read/Write Text Files, Get File Info")
print("  - String operations: Split, Join, Replace Text")
print("  - List operations: Filter, Sort, Random Choice")
print("  - Logic operations: Conditional String, String/Number Comparison, Logical Operations, Counter")
print("  - System operations: Environment Variables")
print("  - Data operations: JSON Processor")
print("  - Code operations: Python Code Editor, Python Code Editor (Advanced)")
print("  Total nodes available:", len(NODE_CLASS_MAPPINGS))
