# Python代码编辑器节点 (Python Code Editor Node)

这是一个为ComfyUI设计的Python代码编辑器和执行器节点，允许您在ComfyUI工作流中编写和执行Python代码。

## 功能特性

- **代码编辑**: 多行文本输入框，支持编写复杂的Python代码
- **代码执行**: 在指定的Python环境中安全执行代码
- **语法检查**: 在不执行代码的情况下检查语法错误
- **输入数据支持**: 可以向代码中注入外部数据
- **结果提取**: 自动提取指定变量的值作为返回结果
- **错误处理**: 完善的错误捕获和报告机制
- **超时保护**: 防止代码无限执行

## 节点参数

### 必需参数

- **python_code**: 要执行的Python代码（多行文本）
- **execution_mode**: 执行模式
  - `execute`: 执行代码
  - `syntax_check`: 仅检查语法
- **timeout_seconds**: 执行超时时间（1-300秒，默认30秒）
- **capture_output**: 是否捕获输出（默认True）
- **return_variable**: 要返回的变量名（默认"result"）

### 可选参数

- **input_data**: 输入数据（会作为`input_data`变量注入到代码中）

## 返回值

- **output**: 代码执行的标准输出
- **result_value**: 指定变量的值
- **success**: 执行是否成功（布尔值）
- **execution_info**: 执行信息和错误详情

## 使用示例

### 1. 基本数学计算

```python
# 计算圆的面积
import math

radius = 5
area = math.pi * radius ** 2
result = f"半径为{radius}的圆的面积是{area:.2f}"
print(result)
```

### 2. 字符串处理

```python
# 处理文本
text = "Hello ComfyUI!"
processed = text.upper().replace("HELLO", "你好")
result = processed
print(f"处理后的文本: {result}")
```

### 3. 使用输入数据

```python
# 处理输入的JSON数据
import json

data = json.loads(input_data)
name = data.get("name", "Unknown")
age = data.get("age", 0)

result = f"Hello {name}, you are {age} years old!"
print(result)
```

输入数据示例: `{"name": "Alice", "age": 25}`

### 4. 数据分析

```python
# 分析数字列表
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

total = sum(numbers)
average = total / len(numbers)
maximum = max(numbers)
minimum = min(numbers)

result = {
    "total": total,
    "average": average,
    "max": maximum,
    "min": minimum
}

print(f"分析结果: {result}")
```

## Python环境

节点使用以下Python解释器路径：
```
K:\AI_V2\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
```

如果指定路径不存在，会自动回退到系统默认Python环境。

## 安全注意事项

1. **代码执行**: 此节点会执行任意Python代码，请确保代码来源可信
2. **文件访问**: 代码可以访问文件系统，请谨慎处理文件操作
3. **网络访问**: 代码可以进行网络请求，请注意安全性
4. **超时设置**: 建议设置合理的超时时间防止无限循环

## 最佳实践

1. **使用result变量**: 将最终结果赋值给`result`变量以便节点提取
2. **添加打印语句**: 使用`print()`输出调试信息
3. **错误处理**: 在代码中添加try-except块处理可能的错误
4. **模块导入**: 可以导入Python标准库和已安装的第三方库
5. **数据验证**: 处理输入数据时进行适当的验证

## 故障排除

### 常见问题

1. **语法错误**: 使用语法检查模式先验证代码
2. **导入错误**: 确保所需模块在Python环境中已安装
3. **超时错误**: 增加超时时间或优化代码性能
4. **权限错误**: 确保Python进程有足够的文件系统访问权限

### 调试技巧

1. 使用`print()`语句输出中间结果
2. 先用简单代码测试节点功能
3. 检查execution_info输出获取详细错误信息
4. 使用语法检查模式验证代码正确性

## 版本信息

- 节点版本: 1.0
- 支持的Python版本: 3.7+
- ComfyUI兼容性: 所有版本

## 更新日志

### v1.0 (2024-08-10)
- 初始版本发布
- 支持基本的Python代码执行
- 添加语法检查功能
- 实现输入数据注入
- 添加超时和错误处理机制
