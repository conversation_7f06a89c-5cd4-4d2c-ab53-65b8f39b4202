#!/usr/bin/env python3
"""
完整功能演示 - Python代码编辑器节点
展示基础版和高级版的所有功能
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from logic_operations import PythonCodeEditor, PythonCodeEditorAdvanced

def demo_basic_editor():
    """演示基础版Python代码编辑器"""
    print("🔧 基础版Python代码编辑器演示")
    print("=" * 50)
    
    editor = PythonCodeEditor()
    
    # 演示代码
    demo_code = """
# 基础版演示代码
print("欢迎使用Python代码编辑器!")

# 处理输入数据
if input_data:
    print(f"输入数据: {input_data}")

# 处理输入数组
if input_array:
    print(f"输入数组: {input_array}")
    print(f"数组长度: {len(input_array)}")
    
    # 计算数字项目的总和
    numeric_sum = sum(x for x in input_array if isinstance(x, (int, float)))
    print(f"数字项目总和: {numeric_sum}")

# 使用输入数字
print(f"输入数字: {input_number}")
print(f"数字的平方: {input_number ** 2}")

# 生成结果
result = f"处理完成! 数组长度: {len(input_array) if input_array else 0}, 数字: {input_number}"
print(f"最终结果: {result}")
"""
    
    # 执行演示
    output, result_value, success, execution_info = editor.execute_python_code(
        python_code=demo_code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        input_data="这是测试数据",
        input_array=[1, 2, 3, "hello", 4.5],
        input_number=42.0
    )
    
    print(f"📤 输出:\n{output}")
    print(f"🎯 结果值: {result_value}")
    print(f"✅ 成功: {success}")
    print(f"ℹ️ 执行信息: {execution_info}")
    print()

def demo_advanced_editor():
    """演示高级版Python代码编辑器"""
    print("🚀 高级版Python代码编辑器演示")
    print("=" * 50)
    
    editor = PythonCodeEditorAdvanced()
    
    # 高级演示代码
    advanced_code = """
# 高级版演示代码 - 自动导入了常用模块
print("🚀 高级Python代码编辑器启动!")

# 使用预导入的模块
print(f"当前工作目录: {os.getcwd()}")
print(f"Python版本: {sys.version.split()[0]}")

# JSON数据处理
if input_data:
    try:
        data_obj = json.loads(input_data)
        print(f"解析JSON成功: {data_obj}")
    except:
        print(f"作为普通字符串处理: {input_data}")

# 高级数组分析
if input_array:
    print(f"\\n📊 数组分析:")
    print(f"  总项目数: {len(input_array)}")
    
    # 按类型分组
    type_groups = {}
    for item in input_array:
        item_type = type(item).__name__
        if item_type not in type_groups:
            type_groups[item_type] = []
        type_groups[item_type].append(item)
    
    for type_name, items in type_groups.items():
        print(f"  {type_name}: {len(items)} 个 - {items}")
    
    # 数学运算 (仅对数字)
    numbers = [x for x in input_array if isinstance(x, (int, float))]
    if numbers:
        print(f"\\n🔢 数学统计:")
        print(f"  数字个数: {len(numbers)}")
        print(f"  总和: {sum(numbers)}")
        print(f"  平均值: {sum(numbers) / len(numbers):.2f}")
        print(f"  最大值: {max(numbers)}")
        print(f"  最小值: {min(numbers)}")
        
        # 使用numpy进行高级计算
        numbers_array = np.array(numbers)
        print(f"  标准差: {np.std(numbers_array):.2f}")

# 数学函数演示
if input_number > 0:
    print(f"\\n🧮 数学函数演示:")
    print(f"  原数字: {input_number}")
    print(f"  平方根: {math.sqrt(input_number):.3f}")
    print(f"  正弦值: {math.sin(input_number):.3f}")
    print(f"  自然对数: {math.log(input_number):.3f}")

# 创建复杂结果对象
result = {
    "summary": "高级Python代码编辑器演示完成",
    "inputs_processed": {
        "data_length": len(input_data) if input_data else 0,
        "array_items": len(input_array) if input_array else 0,
        "number_value": input_number
    },
    "analysis": {
        "type_distribution": type_groups if input_array else {},
        "numeric_stats": {
            "count": len([x for x in (input_array or []) if isinstance(x, (int, float))]),
            "sum": sum([x for x in (input_array or []) if isinstance(x, (int, float))])
        } if input_array else {}
    },
    "timestamp": "2024-08-10",
    "version": "2.0"
}

print(f"\\n📋 最终结果对象已生成")
"""
    
    # 执行高级演示
    test_json = '{"name": "高级测试", "version": 2.0, "features": ["数组支持", "UI增强", "语法高亮"]}'
    test_array = [1, 2, 3, "测试", 4.5, True, None, 6, 7.8, "完成"]
    test_number = 16.0
    
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=advanced_code,
        execution_mode="execute",
        timeout_seconds=30,
        capture_output=True,
        return_variable="result",
        open_editor=False,
        input_data=test_json,
        input_array=test_array,
        input_number=test_number,
        input_image=None
    )
    
    print(f"📤 输出:\n{output}")
    print(f"🎯 结果值: {result_value}")
    print(f"📦 结果对象类型: {type(result_object)}")
    print(f"✅ 成功: {success}")
    print(f"ℹ️ 执行信息: {execution_info}")
    print()

def demo_syntax_checking():
    """演示语法检查功能"""
    print("🔍 语法检查功能演示")
    print("=" * 50)
    
    editor = PythonCodeEditorAdvanced()
    
    # 正确的代码
    valid_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

result = [fibonacci(i) for i in range(8)]
"""
    
    print("✅ 检查正确的代码:")
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=valid_code,
        execution_mode="syntax_check",
        timeout_seconds=10,
        capture_output=True,
        return_variable="result"
    )
    print(f"结果: {execution_info}")
    
    # 错误的代码
    invalid_code = """
def broken_function(:
    print("缺少参数和括号"
    return "错误"
    
result = broken_function(
"""
    
    print("\n❌ 检查错误的代码:")
    output, result_value, success, execution_info, result_object = editor.execute_python_code_advanced(
        python_code=invalid_code,
        execution_mode="syntax_check",
        timeout_seconds=10,
        capture_output=True,
        return_variable="result"
    )
    print(f"结果: {execution_info}")
    print()

def demo_ui_features():
    """演示UI功能特性"""
    print("🎨 UI功能特性说明")
    print("=" * 50)
    
    print("📁 已创建的文件:")
    print("  ✓ web/extensions/python_code_editor.js - JavaScript前端扩展")
    print("  ✓ web/extensions/python_code_editor.css - CSS样式文件")
    print("  ✓ logic_operations.py - 更新的节点实现")
    print("  ✓ 各种测试和文档文件")
    
    print("\n🎨 UI增强功能:")
    print("  ✓ 代码编辑器深色主题")
    print("  ✓ 等宽字体 (Consolas, Monaco)")
    print("  ✓ 语法高亮样式")
    print("  ✓ 智能缩进和Tab处理")
    print("  ✓ 可调整大小的编辑器")
    print("  ✓ 外部编辑器窗口 (高级版)")
    print("  ✓ 工具栏和状态栏")
    print("  ✓ 快捷键支持")
    
    print("\n🖱️ 外部编辑器功能:")
    print("  ✓ 800x600独立窗口")
    print("  ✓ 保存&关闭按钮")
    print("  ✓ 语法检查按钮")
    print("  ✓ 代码模板插入")
    print("  ✓ 光标位置显示")
    print("  ✓ Ctrl+S快捷保存")
    
    print("\n📋 使用说明:")
    print("  1. 重启ComfyUI以加载新的UI功能")
    print("  2. 在节点菜单中找到:")
    print("     - Common Utility/Code Operations/Python Code Editor")
    print("     - Common Utility/Code Operations/Python Code Editor (Advanced)")
    print("  3. 高级版可点击'open_editor'按钮打开外部编辑器")
    print("  4. 连接各种输入类型测试功能")
    print()

if __name__ == "__main__":
    print("🎉 Python代码编辑器节点 - 完整功能演示")
    print("=" * 60)
    print()
    
    demo_basic_editor()
    demo_advanced_editor()
    demo_syntax_checking()
    demo_ui_features()
    
    print("🎊 演示完成!")
    print("\n📋 总结:")
    print("✅ 基础版Python代码编辑器 - 支持字符串、数组、数字输入")
    print("🚀 高级版Python代码编辑器 - 增强UI、外部编辑器、图像支持")
    print("🎨 前端UI增强 - 语法高亮、深色主题、智能编辑")
    print("🔍 语法检查 - 详细错误报告和位置指示")
    print("📊 多输入支持 - 字符串、数组、数字、图像")
    print("🛠️ 开发工具 - 外部编辑器、模板、快捷键")
    print("\n🚀 准备在ComfyUI中使用! 记得重启ComfyUI以加载UI增强功能。")
