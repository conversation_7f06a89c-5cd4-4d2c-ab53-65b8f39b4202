/* Python Code Editor Styles for ComfyUI */

/* Enhanced multiline input for Python code */
.comfy-multiline-input.python-code-editor {
    font-family: 'Consolas', 'Monaco', 'Courier New', 'SF Mono', monospace !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
    background-color: #1e1e1e !important;
    color: #d4d4d4 !important;
    border: 1px solid #3c3c3c !important;
    border-radius: 6px !important;
    padding: 12px !important;
    tab-size: 4 !important;
    white-space: pre !important;
    overflow-wrap: normal !important;
    overflow-x: auto !important;
    resize: both !important;
    min-height: 200px !important;
    max-height: 600px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    transition: border-color 0.2s ease !important;
}

.comfy-multiline-input.python-code-editor:focus {
    border-color: #007acc !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2) !important;
}

.comfy-multiline-input.python-code-editor:hover {
    border-color: #505050 !important;
}

/* Syntax highlighting classes (basic) */
.python-code-editor .keyword {
    color: #569cd6 !important;
    font-weight: bold !important;
}

.python-code-editor .string {
    color: #ce9178 !important;
}

.python-code-editor .comment {
    color: #6a9955 !important;
    font-style: italic !important;
}

.python-code-editor .number {
    color: #b5cea8 !important;
}

.python-code-editor .function {
    color: #dcdcaa !important;
}

/* Node styling for Python Code Editor nodes */
.comfy-node[data-title*="Python Code Editor"] {
    border-color: #007acc !important;
}

.comfy-node[data-title*="Python Code Editor"] .comfy-title {
    background-color: #007acc !important;
    color: white !important;
}

/* Button styling for open editor button */
.comfy-node .comfy-widget-value[data-widget-name="open_editor"] {
    background-color: #0e639c !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 6px 12px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    transition: background-color 0.2s ease !important;
}

.comfy-node .comfy-widget-value[data-widget-name="open_editor"]:hover {
    background-color: #1177bb !important;
}

/* Tooltip styling */
.python-code-tooltip {
    position: absolute;
    background-color: #2d2d30;
    color: #cccccc;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
    border: 1px solid #3c3c3c;
}

/* Execution status indicator */
.python-execution-status {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
    vertical-align: middle;
}

.python-execution-status.success {
    background-color: #4caf50;
}

.python-execution-status.error {
    background-color: #f44336;
}

.python-execution-status.running {
    background-color: #ff9800;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Code editor toolbar */
.python-code-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    background-color: #2d2d30;
    border-bottom: 1px solid #3c3c3c;
    border-radius: 6px 6px 0 0;
    font-size: 12px;
    color: #cccccc;
}

.python-code-toolbar button {
    background-color: #0e639c;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 11px;
    transition: background-color 0.2s ease;
}

.python-code-toolbar button:hover {
    background-color: #1177bb;
}

.python-code-toolbar .info {
    margin-left: auto;
    font-size: 11px;
    color: #999;
}

/* Widget container styling */
.comfy-widget-container.python-code-widget {
    border: 1px solid #3c3c3c;
    border-radius: 6px;
    overflow: hidden;
    background-color: #1e1e1e;
}

.comfy-widget-container.python-code-widget .python-code-toolbar {
    border-radius: 0;
}

.comfy-widget-container.python-code-widget .comfy-multiline-input {
    border: none;
    border-radius: 0;
    margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .comfy-multiline-input.python-code-editor {
        font-size: 12px !important;
        min-height: 150px !important;
    }
    
    .python-code-toolbar {
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .python-code-toolbar button {
        font-size: 10px;
        padding: 3px 6px;
    }
}

/* Dark theme adjustments */
.comfy-ui-dark .comfy-multiline-input.python-code-editor {
    background-color: #0d1117 !important;
    color: #e6edf3 !important;
    border-color: #30363d !important;
}

.comfy-ui-dark .comfy-multiline-input.python-code-editor:focus {
    border-color: #1f6feb !important;
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.2) !important;
}

/* Light theme adjustments */
.comfy-ui-light .comfy-multiline-input.python-code-editor {
    background-color: #ffffff !important;
    color: #24292f !important;
    border-color: #d0d7de !important;
}

.comfy-ui-light .comfy-multiline-input.python-code-editor:focus {
    border-color: #0969da !important;
    box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.2) !important;
}

/* Scrollbar styling for code editor */
.comfy-multiline-input.python-code-editor::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

.comfy-multiline-input.python-code-editor::-webkit-scrollbar-track {
    background-color: #2d2d30;
    border-radius: 6px;
}

.comfy-multiline-input.python-code-editor::-webkit-scrollbar-thumb {
    background-color: #555;
    border-radius: 6px;
    border: 2px solid #2d2d30;
}

.comfy-multiline-input.python-code-editor::-webkit-scrollbar-thumb:hover {
    background-color: #777;
}

.comfy-multiline-input.python-code-editor::-webkit-scrollbar-corner {
    background-color: #2d2d30;
}

/* Selection styling */
.comfy-multiline-input.python-code-editor::selection {
    background-color: #264f78 !important;
    color: inherit !important;
}

.comfy-multiline-input.python-code-editor::-moz-selection {
    background-color: #264f78 !important;
    color: inherit !important;
}

/* Line numbers (if implemented) */
.python-code-line-numbers {
    background-color: #1e1e1e;
    color: #858585;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    padding: 12px 8px;
    border-right: 1px solid #3c3c3c;
    user-select: none;
    text-align: right;
    min-width: 40px;
}

/* Error highlighting */
.python-code-error-line {
    background-color: rgba(244, 67, 54, 0.1) !important;
    border-left: 3px solid #f44336 !important;
}

/* Success highlighting */
.python-code-success-line {
    background-color: rgba(76, 175, 80, 0.1) !important;
    border-left: 3px solid #4caf50 !important;
}
