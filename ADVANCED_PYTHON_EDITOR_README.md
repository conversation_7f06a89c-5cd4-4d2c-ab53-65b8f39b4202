# 高级Python代码编辑器节点 (Advanced Python Code Editor Node)

这是一个为ComfyUI设计的增强版Python代码编辑器，提供了更好的用户界面体验和强大的数组处理功能。

## 🆕 新功能特性

### 基础版 (PythonCodeEditor)
- ✅ 多行代码编辑
- ✅ 语法检查
- ✅ 字符串输入支持
- ✅ 数组输入支持 (`input_array`)
- ✅ 数字输入支持 (`input_number`)
- ✅ 基本错误处理

### 高级版 (PythonCodeEditorAdvanced)
- 🎨 **增强的UI体验** - 代码编辑器样式，语法高亮
- 🖱️ **外部编辑器窗口** - 点击按钮打开专用代码编辑器
- 📊 **多种输入类型支持**:
  - `input_data`: 字符串输入
  - `input_array`: 数组/列表输入 (支持任意类型)
  - `input_number`: 数字输入
  - `input_image`: 图像张量输入 (可选)
- 🔍 **增强的语法检查** - 更详细的错误报告
- 📦 **自动模块导入** - 预导入常用模块 (sys, os, json, math, numpy)
- 🎯 **结果对象解析** - 自动解析返回的Python对象
- ⚡ **更好的错误处理** - 详细的错误信息和堆栈跟踪

## 📋 节点参数

### 必需参数
- **python_code**: Python代码 (多行文本，支持语法高亮)
- **execution_mode**: 执行模式
  - `execute`: 执行代码
  - `syntax_check`: 仅检查语法
- **timeout_seconds**: 超时时间 (1-300秒)
- **capture_output**: 是否捕获输出
- **return_variable**: 返回变量名 (默认"result")
- **open_editor**: 打开外部编辑器 (仅高级版)

### 可选参数
- **input_data**: 字符串输入数据
- **input_array**: 数组/列表输入 (支持任意类型)
- **input_number**: 数字输入
- **input_image**: 图像张量输入 (仅高级版)

### 返回值
- **output**: 代码执行的标准输出
- **result_value**: 指定变量的值
- **success**: 执行是否成功
- **execution_info**: 执行信息和错误详情
- **result_object**: 解析后的结果对象 (仅高级版)

## 🎨 UI增强功能

### 代码编辑器样式
- 🎯 **等宽字体**: Consolas, Monaco, Courier New
- 🌙 **深色主题**: 类似VS Code的深色编辑器
- 📏 **语法高亮**: 基础的Python语法着色
- 🔧 **智能缩进**: 自动缩进和Tab键处理
- 📐 **行号显示**: 可选的行号显示
- 🎚️ **可调大小**: 支持拖拽调整编辑器大小

### 外部编辑器窗口
- 🖼️ **专用窗口**: 800x600像素的独立编辑器窗口
- 🛠️ **工具栏**: 保存、语法检查、插入模板按钮
- 📊 **状态栏**: 显示光标位置和状态信息
- ⌨️ **快捷键**: Ctrl+S保存，Tab缩进，自动缩进
- 📝 **模板插入**: 预定义的代码模板

## 💡 使用示例

### 1. 基本数组处理

```python
# 处理输入数组
if input_array:
    # 统计分析
    numeric_items = [x for x in input_array if isinstance(x, (int, float))]
    total = sum(numeric_items)
    average = total / len(numeric_items) if numeric_items else 0
    
    result = f"数组包含 {len(input_array)} 个项目，数字项目总和: {total}，平均值: {average:.2f}"
else:
    result = "没有提供数组输入"

print(result)
```

### 2. 混合数据类型处理

```python
import json

# 处理多种输入类型
data_summary = {
    "input_data_length": len(input_data),
    "array_length": len(input_array) if input_array else 0,
    "number_value": input_number,
    "has_image": input_image is not None
}

# 分析数组内容
if input_array:
    type_counts = {}
    for item in input_array:
        item_type = type(item).__name__
        type_counts[item_type] = type_counts.get(item_type, 0) + 1
    
    data_summary["array_type_distribution"] = type_counts

result = json.dumps(data_summary, indent=2)
print(f"数据分析结果:\n{result}")
```

### 3. 数学计算和可视化

```python
import math

# 数学运算示例
if input_array and all(isinstance(x, (int, float)) for x in input_array):
    # 统计计算
    data = input_array
    n = len(data)
    mean = sum(data) / n
    variance = sum((x - mean) ** 2 for x in data) / n
    std_dev = math.sqrt(variance)
    
    # 应用输入数字作为缩放因子
    scaled_data = [x * input_number for x in data]
    
    result = {
        "original_data": data,
        "statistics": {
            "count": n,
            "mean": round(mean, 3),
            "variance": round(variance, 3),
            "std_deviation": round(std_dev, 3)
        },
        "scaled_data": scaled_data,
        "scale_factor": input_number
    }
else:
    result = "需要数字数组进行数学计算"

print(f"数学分析完成: {result}")
```

### 4. 图像处理相关 (高级版)

```python
# 图像处理示例 (当连接图像输入时)
if input_image is not None:
    # 在实际实现中，input_image 将是图像张量
    result = f"接收到图像输入，准备处理..."
    
    # 这里可以添加图像处理逻辑
    # 例如: 调整大小、滤镜、分析等
    
else:
    # 模拟图像处理参数
    width = int(input_number) if input_number > 0 else 512
    height = int(width * 0.75)  # 4:3 比例
    
    result = {
        "image_params": {
            "width": width,
            "height": height,
            "aspect_ratio": round(width / height, 2)
        },
        "processing_options": input_array if input_array else ["resize", "filter", "enhance"]
    }

print(f"图像处理结果: {result}")
```

## 🚀 使用方法

### 基础版使用
1. 在ComfyUI中添加 `Python Code Editor` 节点
2. 在代码框中编写Python代码
3. 连接所需的输入 (input_data, input_array, input_number)
4. 运行工作流

### 高级版使用
1. 在ComfyUI中添加 `Python Code Editor (Advanced)` 节点
2. 选择编辑方式:
   - **直接编辑**: 在节点的代码框中编写
   - **外部编辑器**: 点击 `open_editor` 按钮打开专用编辑器
3. 连接各种输入类型
4. 运行工作流查看结果

### 外部编辑器使用技巧
- **Ctrl+S**: 保存并关闭编辑器
- **Tab/Shift+Tab**: 缩进/取消缩进
- **Enter**: 自动缩进新行
- **语法检查**: 点击工具栏按钮检查语法
- **插入模板**: 快速插入常用代码模板

## 🔧 安装和配置

1. **重启ComfyUI** - 新的UI功能需要重启才能生效
2. **检查文件** - 确保以下文件存在:
   - `web/extensions/python_code_editor.js`
   - `web/extensions/python_code_editor.css`
3. **浏览器缓存** - 如果样式没有生效，清除浏览器缓存

## 🛠️ 故障排除

### 常见问题
1. **样式没有生效**: 重启ComfyUI并清除浏览器缓存
2. **外部编辑器无法打开**: 检查浏览器弹窗阻止设置
3. **数组输入不工作**: 确保连接的是支持数组输出的节点
4. **语法错误**: 使用语法检查模式先验证代码

### 调试技巧
1. 使用 `print()` 语句输出调试信息
2. 检查 `execution_info` 输出获取详细错误
3. 先用简单代码测试节点功能
4. 使用语法检查模式验证代码

## 📊 性能优化

- **超时设置**: 根据代码复杂度调整超时时间
- **数组大小**: 大数组可能需要更长的处理时间
- **内存使用**: 避免创建过大的数据结构
- **模块导入**: 利用预导入的模块提高性能

## 🔄 版本信息

- **节点版本**: 2.0
- **支持的Python版本**: 3.7+
- **ComfyUI兼容性**: 所有版本
- **前端要求**: 现代浏览器 (Chrome, Firefox, Safari, Edge)

## 📝 更新日志

### v2.0 (2024-08-10)
- ✨ 新增高级版Python代码编辑器
- 🎨 添加UI增强功能和语法高亮
- 🖱️ 实现外部编辑器窗口
- 📊 支持多种输入类型 (数组、数字、图像)
- 🔍 增强语法检查和错误报告
- 📦 自动导入常用模块
- 🎯 结果对象自动解析

### v1.0 (2024-08-10)
- 🎉 初始版本发布
- ✅ 基本Python代码执行功能
- 🔍 语法检查功能
- 📝 输入数据注入
- ⏱️ 超时和错误处理机制
